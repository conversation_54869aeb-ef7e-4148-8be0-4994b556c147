"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";

import Link from "next/link";
import Image from "next/image";
import { useSelector } from "react-redux";
import { AuthState } from "@/redux/slices/authSlice";
import { useTranslations } from "next-intl";

import { syncReduxStateToCookies } from "@/utils/syncReduxToCookies";
import Logo from "../../../public/assets/images/logo.svg";
import downArrow from "../../../public/assets/images/down-arrow.svg";
import styles from "@/styles/header.module.scss";
import NotificationIcon from "../svgComponents/Notification";
import { logout, getUserInitials } from "@/utils/helper";
import { usePathname, useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { selectProfileData, setPermissions, selectRole } from "@/redux/slices/authSlice";
import { getUserPermissions } from "@/services/authServices";

// Interface definitions moved to authServices.ts
import DataSecurityIcon from "../svgComponents/dataSecurityIcon";
import ROUTES from "@/constants/routes";
import { IUserData } from "@/interfaces/authInterfaces";
import { PERMISSION } from "@/constants/commonConstants";
import { useHasPermission } from "@/utils/permission";
import ProfileIcon from "../svgComponents/ProfileIcon";
import LogoutIcon from "../svgComponents/LogoutIcon";
import Notifications from "../views/notification/Notifications";
import { RootState } from "@/redux/store";
import { getUnreadNotificationsCount } from "@/services/notificationServices/notificationService";
import { setHasUnreadNotification } from "@/redux/slices/notificationSlice";
import NavCalendarIcon from "../svgComponents/NavCalendarIcon";
import NavCandidatesIcon from "../svgComponents/NavCandidatesIcon";
import NavHomeIcon from "../svgComponents/NavHomeIcon";
import NavJobsIcon from "../svgComponents/NavJobsIcon";
import NavSettingsIcon from "../svgComponents/NavSettingsIcon";
import HistoryIcon from "../svgComponents/HistoryIcon";
import MobileSidebar from "./MobileSidebar";
import Button from "../formElements/Button";
import HamburgerIcon from "../svgComponents/HamburgerIcon";
import ManageUsersIcon from "../svgComponents/ManageUsersIcon";
import RolesPermissionsIcon from "../svgComponents/RolesPermissionsIcon";
import ActivityLogsIcon from "../svgComponents/ActivityLogsIcon";
import ArchiveJobsCandidatesIcon from "../svgComponents/ArchiveJobsCandidatesIcon";
import { useAuth } from "@/hooks/useAuth";

const Header = () => {
  const [dropdown, SetDropdown] = useState(false);
  const userProfile: IUserData | null = useSelector(selectProfileData);
  const userRole = useSelector(selectRole);
  const [imageError, setImageError] = useState<boolean>(false);

  const [isSidebarOpen, setSidebarOpen] = useState(false);
  const [subLink, setSubLink] = useState(false);
  const [history, setHistory] = useState(false);
  const path = usePathname();
  const dispatch = useDispatch();
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  console.log("authData==========================> in header:", authData);
  const t = useTranslations("header");
  const tCommon = useTranslations("common");
  const pathname = usePathname();
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const dropdownRef = useRef<HTMLUListElement>(null);
  const hasUnreadNotification = useSelector((state: RootState) => state.notification.hasUnreadNotifications);

  const navigate = useRouter();
  const { isAuthenticated } = useAuth();

  // Permission checks
  const hasCreateNewRolePermission = useHasPermission(PERMISSION.CREATE_NEW_ROLE);
  const hasManageUserPermissionsPermission = useHasPermission(PERMISSION.MANAGE_USER_PERMISSIONS);
  const hasCreateNewDepartmentPermission = useHasPermission(PERMISSION.CREATE_NEW_DEPARTMENT);
  const hasAddEmployeePermission = useHasPermission(PERMISSION.ADD_EMPLOYEE);
  const hasViewHiredCandidatesPermission = useHasPermission(PERMISSION.VIEW_HIRED_CANDIDATES);
  const hasHireCandidatePermission = useHasPermission(PERMISSION.HIRE_CANDIDATE);
  const hasCandidatesPermission = hasViewHiredCandidatesPermission || hasHireCandidatePermission;

  // History dropdown permissions
  const hasViewAuditLogsPermission = useHasPermission(PERMISSION.VIEW_AUDIT_LOGS_UPCOMING);
  const hasArchiveRestoreCandidatesPermission = useHasPermission(PERMISSION.ARCHIVE_RESTORE_CANDIDATES);
  const hasArchiveRestoreJobsPermission = useHasPermission(PERMISSION.ARCHIVE_RESTORE_JOB_POSTS);
  const hasArchivePermission = hasArchiveRestoreCandidatesPermission || hasArchiveRestoreJobsPermission;
  const hasAnyHistoryPermission = hasViewAuditLogsPermission || hasArchivePermission;

  // Reset image error when user profile changes
  useEffect(() => {
    setImageError(false);
  }, [userProfile?.image]);

  // Handle clicks outside of dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !(event.target instanceof HTMLElement && event.target.id.includes("dropdown"))
      ) {
        SetDropdown(false);
        // Reset nested dropdown states when main dropdown closes
        setSubLink(false);
        setHistory(false);
      }
    };

    // Add event listener when dropdown is open
    if (dropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    // Clean up event listener
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdown]);

  // Toggle dropdown visibility
  const MenuDropdown = () => {
    const newDropdownState = !dropdown;
    SetDropdown(newDropdownState);

    // If closing the dropdown, also reset nested dropdowns
    if (!newDropdownState) {
      setSubLink(false);
      setHistory(false);
    }
  };

  // Function to fetch permissions using the authServices
  const fetchPermissions = useCallback(async () => {
    try {
      const response = await getUserPermissions();

      // Only update Redux store when success is true
      if (response.data?.success) {
        dispatch(setPermissions(response.data.data.rolePermissions));
        // Sync Redux state to cookies after updating permissions
        syncReduxStateToCookies(response.data.data.rolePermissions, true);
      } else {
        console.log("Permission fetch unsuccessful:", response.data?.message);
      }
    } catch (error) {
      console.error("Error fetching permissions:", error);
    }
  }, [path, dispatch]);

  const getUserNotificationsUnreadStatus = useCallback(async () => {
    try {
      const response = await getUnreadNotificationsCount();
      if (response.data?.success) {
        const hasUnreadNotifications = response.data.data.count > 0;
        dispatch(setHasUnreadNotification(hasUnreadNotifications));
      } else {
        console.error("Failed to fetch unread notifications status:", response.data?.message);
      }
    } catch (error) {
      console.error("Error fetching unread notifications status:", error);
    }
  }, [path, dispatch]);

  // Sync Redux state to cookies after mounting component
  useEffect(() => {
    syncReduxStateToCookies();
  }, []);

  useEffect(() => {
    // Check if this is first mount or a genuine route change
    if (isAuthenticated) {
      fetchPermissions();
      getUserNotificationsUnreadStatus();
    }
  }, [path, dispatch, fetchPermissions, isAuthenticated, getUserNotificationsUnreadStatus]);

  /**
   * Logs out the user if the access token is invalid.
   * If the access token is invalid, it logs out the user and shows a toast message.
   */

  // const logoutUser = async () => {
  //   const token = getAccessToken();
  //   if (!token) {
  //     onHandleLogout();
  //     toast.dismiss();
  //     toastMessageError(t("session_expired"));
  //   }
  // };

  const onHandleLogout = async () => {
    await logout(authData?.id);

    if (typeof window !== "undefined") {
      window.location.reload();
    }
  };

  const SidebarDropdown = () => {
    setSidebarOpen(!isSidebarOpen);
  };

  return (
    <>
      <header
        className={styles.header}
        // className={`${styles.header} ${isVisible ? "" : `${styles.hidden}`}`}
      >
        <nav className="navbar navbar-expand-sm">
          <div className="container">
            <div className="d-flex align-items-center justify-content-between w-100">
              <Link className="navbar-brand" href={ROUTES.HOME}>
                <Image src={Logo} alt="logo" width={640} height={320} className={styles.logo} />
              </Link>
              {authData && authData?.email.length > 0 ? (
                <div className={styles.hamburger_icon}>
                  <Button onClick={SidebarDropdown} className="clear-btn p-0 primary hamburger">
                    <HamburgerIcon />
                  </Button>{" "}
                </div>
              ) : (
                <Button onClick={() => navigate.push(ROUTES.LOGIN)} className="primary btn">
                  Login
                </Button>
              )}

              {authData && authData?.email.length > 0 ? (
                <ul className="header_links">
                  <li className={pathname === ROUTES.DASHBOARD ? "active" : ""} onClick={() => navigate.push(ROUTES.DASHBOARD)}>
                    <NavHomeIcon /> Dashboard
                  </li>
                  <li
                    className={
                      pathname === ROUTES.JOBS.ACTIVE_JOBS ||
                      pathname === ROUTES.JOBS.JOB_EDITOR ||
                      pathname.startsWith(ROUTES.SCREEN_RESUME.CANDIDATE_LIST)
                        ? "active"
                        : ""
                    }
                    onClick={() => navigate.push(ROUTES.JOBS.ACTIVE_JOBS)}
                  >
                    <NavJobsIcon /> Active Jobs
                  </li>
                  <li className={pathname === ROUTES.INTERVIEW.CALENDAR ? "active" : ""} onClick={() => navigate.push(ROUTES.INTERVIEW.CALENDAR)}>
                    <NavCalendarIcon /> Calendar
                  </li>
                  {hasCandidatesPermission && (
                    <li
                      className={pathname === ROUTES.SCREEN_RESUME.HIRED_CANDIDATES ? "active" : ""}
                      onClick={() => navigate.push(ROUTES.SCREEN_RESUME.HIRED_CANDIDATES)}
                    >
                      <NavCandidatesIcon /> Hired
                    </li>
                  )}

                  <span></span>
                </ul>
              ) : null}

              {authData && authData?.email.length > 0 ? (
                <div className={styles.header_right}>
                  <div className={styles.user_drop}>
                    <NotificationIcon
                      hasNotification={hasUnreadNotification}
                      id="notification-icon-id"
                      className={styles.user_drop}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setIsNotificationOpen((prev) => !prev);
                      }}
                    />
                  </div>
                  <div className={`dropdown ${styles.user_drop}`}>
                    <button
                      type="button"
                      className={`dropdown-toggle ${styles.user_drop_btn}`}
                      data-bs-toggle="dropdown"
                      onClick={MenuDropdown}
                      id="dropdown-MenuButton"
                    >
                      <div className={`${styles.circle_img}`}>
                        {userProfile?.image && userProfile.image !== "" && !imageError ? (
                          <Image
                            src={userProfile.image}
                            alt="Profile"
                            width={100}
                            height={100}
                            id="dropdown-user-image"
                            onError={() => setImageError(true)}
                          />
                        ) : (
                          <div className={styles.header_profile_image_fallback} id="dropdown-user-initials">
                            {getUserInitials(userProfile?.first_name, userProfile?.last_name)}
                          </div>
                        )}
                      </div>
                      <div className={styles.admin_info}>
                        <h5 id="dropdown-user-name">{`${userProfile?.first_name}`}</h5>
                        <p className="fs-5 mb-0">
                          {userRole?.roleName && userRole.roleName.length > 10 ? `${userRole.roleName.substring(0, 10)}...` : userRole?.roleName}
                        </p>
                      </div>
                      <Image src={downArrow} alt="downArrow" style={{ rotate: `${dropdown ? "180deg" : "0deg"}` }} id="dropdown-downArrow" />
                    </button>

                    {dropdown && (
                      <ul className={styles.dropdown_menu} ref={dropdownRef} id="dropdown-menu">
                        <li
                          onClick={() => {
                            navigate.push(ROUTES.PROFILE.MY_PROFILE);
                            SetDropdown(false);
                          }}
                        >
                          <ProfileIcon />
                          <span>{t("my_profile")}</span>
                        </li>
                        {(hasCreateNewRolePermission ||
                          hasManageUserPermissionsPermission ||
                          hasCreateNewDepartmentPermission ||
                          hasAddEmployeePermission) && (
                          <li className={styles.sub_menubar} onClick={() => setSubLink(!subLink)}>
                            <div className={styles.sub_menu_list}>
                              <NavSettingsIcon />
                              <span>
                                {t("settings")}{" "}
                                <Image
                                  src={downArrow}
                                  alt="downArrow"
                                  style={{ rotate: `${subLink ? "180deg" : "0deg"}`, width: "13px", marginLeft: "5px" }}
                                />
                              </span>
                            </div>
                            {subLink && (
                              <ul className={`${styles.sidebar_sub_menu}`}>
                                {(hasCreateNewDepartmentPermission || hasAddEmployeePermission) && (
                                  <li
                                    onClick={() => {
                                      navigate.push(ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT);
                                      SetDropdown(false);
                                    }}
                                  >
                                    <ManageUsersIcon /> Manage Users
                                  </li>
                                )}
                                {(hasCreateNewRolePermission || hasManageUserPermissionsPermission) && (
                                  <li
                                    onClick={() => {
                                      navigate.push(ROUTES.ROLE_EMPLOYEES.ROLES_PERMISSIONS);
                                      SetDropdown(false);
                                    }}
                                  >
                                    <RolesPermissionsIcon /> Roles and Permissions
                                  </li>
                                )}
                              </ul>
                            )}
                          </li>
                        )}
                        {hasAnyHistoryPermission && (
                          <li className={styles.sub_menubar} onClick={() => setHistory(!history)}>
                            <div className={styles.sub_menu_list}>
                              <HistoryIcon />
                              <span>
                                History{" "}
                                <Image
                                  src={downArrow}
                                  alt="downArrow"
                                  style={{ rotate: `${history ? "180deg" : "0deg"}`, width: "13px", marginLeft: "5px" }}
                                />
                              </span>
                            </div>
                            {history && (
                              <ul className={`${styles.sidebar_sub_menu}`}>
                                {hasViewAuditLogsPermission && (
                                  <li
                                    onClick={() => {
                                      navigate.push(ROUTES.ACTIVITY_LOGS);
                                      SetDropdown(false);
                                    }}
                                  >
                                    <ActivityLogsIcon /> Activity logs
                                  </li>
                                )}
                                {hasArchivePermission && (
                                  <li
                                    onClick={() => {
                                      navigate.push(ROUTES.ARCHIVE.ARCHIVE_JOBS_CANDIDATES);
                                      SetDropdown(false);
                                    }}
                                  >
                                    <ArchiveJobsCandidatesIcon /> Archive Jobs/Candidates
                                  </li>
                                )}
                              </ul>
                            )}
                          </li>
                        )}
                        <li onClick={() => onHandleLogout()} style={{ cursor: "pointer" }}>
                          <LogoutIcon className="strokeSvg" />
                          <span>Logout</span>
                        </li>
                      </ul>
                    )}
                  </div>
                </div>
              ) : null}

              {/* <div className={`collapse navbar-collapse navbar-desktop justify-content-end ${styles.navbar_content}`} id="collapsibleNavbar"></div> */}
            </div>
          </div>
        </nav>
      </header>
      {isNotificationOpen ? <Notifications setIsNotificationOpen={setIsNotificationOpen} /> : null}

      {authData && authData?.email.length > 0 ? <MobileSidebar isOpen={isSidebarOpen} onClose={() => setSidebarOpen(false)} /> : null}

      {/* common pages information box for  Job Requirement Generation page */}
      {pathname === ROUTES.JOBS.GENERATE_JOB && (
        <div className="information-box">
          <DataSecurityIcon />
          <p>{tCommon("data_security_msg")}</p>
        </div>
      )}
    </>
  );
};

export default Header;
