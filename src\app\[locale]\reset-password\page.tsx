"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import Image from "next/image";

import logo from "../../../../public/assets/images/logo.svg";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import styles from "@/styles/auth.module.scss";
import { resetPasswordValidation } from "@/validations/authValidations";
import { resetPassword } from "@/services/authServices";
import routes from "@/constants/routes";
import { toastMessageSuccess, toastMessageError, decryptInfo } from "@/utils/helper";
import ROUTES from "@/constants/routes";
import { IResetPassword } from "@/interfaces/authInterfaces";
import hidePasswordIcon from "../../../../public/assets/images/hide-password.svg";
import showPasswordIcon from "../../../../public/assets/images/show-password.svg";
import { useTranslate } from "@/utils/translationUtils";

const ResetPassword = () => {
  const translate = useTranslate();

  const [loading, setLoading] = useState(false);
  const [password, setPassword] = useState(true);
  const [confirmPassword, setConfirmPassword] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();
  const info = searchParams?.get("info");
  const [parsedInfo, setParsedInfo] = useState<IResetPassword | null>(null);

  useEffect(() => {
    try {
      if (info) {
        const decryptedInfo = decryptInfo(info);
        if (decryptedInfo) {
          const parsed = JSON.parse(decryptedInfo);
          setParsedInfo(parsed);
        } else {
          moveToLogin();
        }
      }
    } catch (error) {
      console.log("error", error);
      moveToLogin();
    }
  }, [info]);

  const moveToLogin = () => {
    toastMessageError(translate("invalid_or_malformed_url_parameters"));
    router.replace(ROUTES.LOGIN);
  };

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(resetPasswordValidation(translate)),
  });

  const onSubmit = async (data: { new_password: string; confirm_password: string }) => {
    if (!parsedInfo) {
      return;
    }
    setLoading(true);
    try {
      const { new_password } = data;
      const resetData = {
        password: new_password,
        email: parsedInfo?.email,
        otp: parsedInfo?.otp,
      };
      const result = await resetPassword(resetData);
      if (result?.data?.success) {
        setLoading(false);
        toastMessageSuccess(translate(result?.data?.message));
        router.replace(routes.LOGIN);
      } else {
        setLoading(false);
        toastMessageError(translate(result?.data?.message) ?? "something_went_wrong");
      }
    } catch (error) {
      console.error(error);
      toastMessageError(translate("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className={styles.auth_main}>
      <div className="container">
        <div className="row">
          <div className={styles.user_auth_main}>
            <div className="container">
              <div className="row row-center">
                <div className={`${styles.hero_image} col-md-6`}>
                  {/* <div className={styles.client_signature_box}>
                    <p>
                      The challenge is great, the effort is extraordinary, the achievement is life changing, and the impact will become your legacy.
                      Where are you now and what are you willing to change to get to where you want to be?
                    </p>
                    <Image src={ClientSignature} alt="client" />
                  </div> */}
                </div>
                <div className="col-md-6">
                  <div className={styles.form_main}>
                    <div className="text-center">
                      <Image src={logo} alt="logo" className={styles.logo} width={200} height={80} />
                      <h1>
                        {translate("reset_heading")} <span>{translate("password_heading")}</span>
                      </h1>
                    </div>
                    <form onSubmit={handleSubmit(onSubmit)}>
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="new_password" required>
                          {translate("new_password")}
                        </InputWrapper.Label>
                        <Textbox
                          className="form-control"
                          control={control}
                          name="new_password"
                          type={password ? "password" : "text"}
                          align="right"
                          placeholder={translate("password_placeholder")}
                          iconClass="icon-align"
                        >
                          <InputWrapper.Icon onClick={() => setPassword(!password)}>
                            <Image src={password ? hidePasswordIcon : showPasswordIcon} alt="password-icon" />
                          </InputWrapper.Icon>
                        </Textbox>
                        <InputWrapper.Error message={errors?.new_password?.message || ""} />
                      </InputWrapper>

                      <InputWrapper>
                        <InputWrapper.Label htmlFor="confirm_password" required>
                          {translate("confirm_password")}
                        </InputWrapper.Label>
                        <Textbox
                          className="form-control"
                          control={control}
                          name="confirm_password"
                          type={confirmPassword ? "password" : "text"}
                          placeholder={translate("confirm_password_placeholder")}
                          iconClass="icon-align"
                          align="right"
                        >
                          <InputWrapper.Icon onClick={() => setConfirmPassword(!confirmPassword)}>
                            <Image src={confirmPassword ? hidePasswordIcon : showPasswordIcon} alt="password-icon" />
                          </InputWrapper.Icon>
                        </Textbox>
                        <InputWrapper.Error message={errors?.confirm_password?.message || ""} />
                      </InputWrapper>
                      <Button loading={loading} disabled={loading} className="primary-btn rounded-md w-100 mt-5">
                        {translate("reset_password")}
                      </Button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
