import { IInterviewQuestionResponse } from "@/interfaces/interviewInterfaces";
import { toastMessageSuccess } from "@/utils/helper";
import html2pdf from "html2pdf.js";

export interface IQuestionCategory {
  questions: IInterviewQuestionResponse[];
  score: number;
  isLocked?: boolean;
  interviewerName?: string;
}

export interface IGetInterviewSkillQuestionsResponse {
  roleSpecificQuestions: Record<string, IQuestionCategory>;
  cultureSpecificQuestions: Record<string, IQuestionCategory>;
  careerBasedQuestions: IQuestionCategory;
}

const downloadQuestions = (
  careerBasedQuestions: IQuestionCategory,
  roleSpecificQuestions: Record<string, IQuestionCategory>,
  cultureSpecificQuestions: Record<string, IQuestionCategory>,
  t: (key: string) => string,
  interviewerName?: string,
  candidateName?: string,
  jobId?: string
) => {
  // Create HTML content for the PDF
  const htmlContent = `
    <style>
      @media print {
        body { margin: 0; }
        h2, h3 { page-break-after: avoid; }
        .question { page-break-inside: avoid; }
      }
    </style>
    <div style="font-family: Arial, sans-serif; padding: 20px; line-height: 1.6;">
      <h1 style="text-align: center; color: #2c3e50;">Pre-Interview Questions</h1>
      
      <!-- Career Based Questions -->
      <h2 style="color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px;">Career Based Questions</h2>
      <div style="margin-bottom: 20px;">
        ${careerBasedQuestions.questions
          .map(
            (q, index) => `
          <div class="question" style="margin-bottom: 15px;">
            <h3 style="color: #2980b9; font-size: 18px;">${index + 1}. ${q.question}</h3>
          </div>
        `
          )
          .join("")}
      </div>

      <!-- Role Specific Questions -->
      <h2 style="color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px;">Role Specific Questions</h2>
      ${Object.keys(roleSpecificQuestions)
        .map(
          (category) => `
        <div style="margin-bottom: 20px;">
          <h3 style="color: #2980b9; font-size: 20px;">${category}</h3>
          ${roleSpecificQuestions[category].questions
            .map(
              (q, index) => `
            <div class="question" style="margin-bottom: 15px;">
              <h4 style="color: #2980b9; font-size: 16px;">${index + 1}. ${q.question}</h4>
            </div>
          `
            )
            .join("")}
        </div>
      `
        )
        .join("")}

      <!-- Culture Specific Questions -->
      <h2 style="color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px;">Culture Specific Questions</h2>
      ${Object.keys(cultureSpecificQuestions)
        .map(
          (category) => `
        <div style="margin-bottom: 20px;">
          <h3 style="color: #2980b9; font-size: 20px;">${category}</h3>
          ${cultureSpecificQuestions[category].questions
            .map(
              (q, index) => `
            <div class="question" style="margin-bottom: 15px;">
              <h4 style="color: #2980b9; font-size: 16px;">${index + 1}. ${q.question}</h4>
              <p><strong>Skill Type:</strong> ${q.skillTitle}</p>
            </div>
          `
            )
            .join("")}
        </div>
      `
        )
        .join("")}
    </div>
  `;

  // Create a temporary container for the HTML content
  const element = document.createElement("div");
  element.innerHTML = htmlContent;

  // Configure html2pdf options
  const opt = {
    margin: [10, 10, 10, 10], // Top, right, bottom, left margins in mm
    filename: `interview-questions-${jobId}-${interviewerName}-${candidateName}-${new Date().toISOString().replace(/:/g, "-")}.pdf`,
    image: { type: "jpeg", quality: 0.98 },
    html2canvas: { scale: 2, useCORS: true },
    jsPDF: { unit: "mm", format: "a4", orientation: "portrait" },
  };

  // Generate and download the PDF
  html2pdf()
    .from(element)
    .set(opt)
    .save()
    .then(() => {
      toastMessageSuccess(t("questions_downloaded_successfully"));
    });
};

export default downloadQuestions;
