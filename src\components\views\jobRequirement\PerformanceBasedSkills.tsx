"use client";
import React, { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useRouter } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import { useTranslations } from "next-intl";

// Components
import Button from "@/components/formElements/Button";
import Loader from "@/components/loader/Loader";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";

// Services (Redux, constants, interfaces)
import { selectJobDetails } from "@/redux/slices/jobDetailsSlice";
import { setSkillsData, selectRoleSpecificSkills, selectCultureSpecificSkills } from "@/redux/slices/jobSkillsSlice";
import { fetchSkillsFailure, fetchSkillsStart, fetchSkillsSuccess, selectAllSkills } from "@/redux/slices/allSkillsSlice";
import { ISkillData, ISkillCategory, ISkillItem } from "@/interfaces/jobRequirementesInterfaces";
import ROUTES from "@/constants/routes";
import { SKILL_CONSTANTS } from "@/constants/commonConstants";

// CSS
import style from "@/styles/commonPage.module.scss";
import { IDraggedItem, ISkill } from "@/interfaces/performanceBasedSkillsInterface";
import { EDIT_SKILLS_TYPE } from "@/constants/jobRequirementConstant";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";
import DualSideIcon from "@/components/svgComponents/DualSideIcon";
import { getAllSkills } from "@/services/jobRequirements/skillsService";

export const ALL = "all";

function PerformanceBasedSkills() {
  const router = useRouter();
  const dispatch = useDispatch();
  const t = useTranslations();
  const jobDetails = useSelector(selectJobDetails);

  // Get dynamic data from Redux
  const skillCategoriesFromRedux = useSelector(selectAllSkills);
  const roleSpecificSkillsFromRedux = useSelector(selectRoleSpecificSkills);
  const cultureSpecificSkillsFromRedux = useSelector(selectCultureSpecificSkills);

  // Memoize data from Redux to prevent unnecessary re-renders
  const skillCategories = useMemo(() => skillCategoriesFromRedux || [], [skillCategoriesFromRedux]);
  const roleSpecificSkills = useMemo(() => roleSpecificSkillsFromRedux || [], [roleSpecificSkillsFromRedux]);
  const cultureSpecificSkills = useMemo(() => cultureSpecificSkillsFromRedux || [], [cultureSpecificSkillsFromRedux]);

  // Skills state
  const maxRoleSkills = SKILL_CONSTANTS.REQUIRED_ROLE_SKILLS; // 10
  const maxCultureSkills = SKILL_CONSTANTS.REQUIRED_CULTURE_SKILLS; // 5

  // Initialize available skills from dynamic data
  const [availableSkills, setAvailableSkills] = useState<ISkill[]>([]);
  const [selectedSkills1, setSelectedSkills1] = useState<ISkill[]>([]);
  const [selectedSkills2, setSelectedSkills2] = useState<ISkill[]>([]);

  // Category filter state
  const [selectedCategory, setSelectedCategory] = useState<string>(ALL);

  // Drag and drop states
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [draggedItem, setDraggedItem] = useState<IDraggedItem | null>(null);
  const [dragOverArea, setDragOverArea] = useState<string | null>(null);
  const [dropIndex, setDropIndex] = useState(0); // Track the index where item will be inserted

  // UI states
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);

  // Ref for main container
  const containerRef = useRef<HTMLDivElement>(null);

  // Convert dynamic data to our skill format
  const convertToSkillFormat = useCallback((skillCategories: ISkillCategory[]): ISkill[] => {
    const skills: ISkill[] = [];

    skillCategories.forEach((category: ISkillCategory) => {
      if (category.items && Array.isArray(category.items)) {
        category.items.forEach((item: ISkillItem) => {
          skills.push({
            id: item.id,
            name: item.title,
            description: item.description || item.short_description || "",
            category: category.type,
            isAlreadySelected: false,
          });
        });
      }
    });

    return skills;
  }, []);

  // Get unique categories from dynamic data
  const categories = useMemo(() => {
    const categorySet = new Set([ALL]);
    skillCategories.forEach((category: ISkillCategory) => {
      categorySet.add(category.type);
    });
    return Array.from(categorySet);
  }, [skillCategories]);

  // Filter available skills based on category
  const filteredAvailableSkills = availableSkills.filter((skill) => {
    return selectedCategory === ALL || skill.category === selectedCategory;
  });

  // Refs for drag animation
  const dragGhostRef = useRef<HTMLElement | null>(null);
  const dragOffsetRef = useRef({ x: 0, y: 0 });
  const animationFrameRef = useRef<number | null>(null);

  // Update ghost position with fluid animation effect
  const updateGhostPosition = useCallback((clientX: number, clientY: number) => {
    if (!dragGhostRef.current) return;

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    // Use requestAnimationFrame for smooth animation at 60fps
    animationFrameRef.current = requestAnimationFrame(() => {
      if (dragGhostRef.current) {
        const x = clientX - dragOffsetRef.current.x;
        const y = clientY - dragOffsetRef.current.y;

        // Direct position update for immediate feedback
        dragGhostRef.current.style.left = `${x}px`;
        dragGhostRef.current.style.top = `${y}px`;

        // Apply a subtle pulse animation during dragging
        dragGhostRef.current.style.animation = "none";
        // Trigger reflow to restart animation
        void dragGhostRef.current.offsetWidth;
        dragGhostRef.current.style.animation = "dragPulse 1.5s ease-in-out infinite";
      }
    });
  }, []);

  // Drag start handler
  const handleDragStart = useCallback((e: React.MouseEvent, skill: ISkill, source: string) => {
    console.log("Starting drag for:", skill.name, "from source:", source);

    setDraggedItem({ skill, source });
    setIsDragging(true);

    // Calculate offset from cursor to element corner
    const rect = e.currentTarget.getBoundingClientRect();
    dragOffsetRef.current = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };

    // Create ghost element with enhanced visual feedback
    const ghost = (e.currentTarget as HTMLElement).cloneNode(true) as HTMLElement;
    ghost.className = `${ghost.className} drag-ghost`;

    // Set all styles at once for better performance
    Object.assign(ghost.style, {
      width: `${rect.width}px`,
      height: `${rect.height}px`,
      left: `${rect.left}px`,
      top: `${rect.top}px`,
      position: "fixed",
      pointerEvents: "none",
      zIndex: "1000",
      opacity: "0.95",
      boxShadow: "0 10px 25px rgba(0,0,0,0.2)",
      transition: "none", // Remove transition for direct positioning
      willChange: "transform, left, top",
      transformOrigin: "center center",
      filter: "brightness(1.05) contrast(1.02)",
      transform: "scale(1.05)",
    });

    // Add visual highlight to show it's being dragged
    const highlights = ghost.querySelectorAll(".skill-box, .skill-item");
    if (highlights.length > 0) {
      highlights.forEach((el: Element) => {
        const htmlEl = el as HTMLElement;
        htmlEl.style.background = "linear-gradient(45deg, #f0f6ff, #e6f0ff)";
        htmlEl.style.borderColor = "#5a9bef";
        htmlEl.style.boxShadow = "inset 0 0 0 1px rgba(74, 144, 226, 0.4)";
        htmlEl.style.color = "#0056b3";
      });
    }

    document.body.appendChild(ghost);
    dragGhostRef.current = ghost;

    // Style original element
    (e.currentTarget as HTMLElement).classList.add("dragging-original");

    e.preventDefault();
  }, []);

  // Mouse move handler for cursor following
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !dragGhostRef.current) return;

      updateGhostPosition(e.clientX, e.clientY);

      // Update drop zone highlighting
      const elementBelow = document.elementFromPoint(e.clientX, e.clientY);
      const dropZone = elementBelow?.closest("[data-drop-area]");

      if (dropZone) {
        const area = dropZone.getAttribute("data-drop-area");
        setDragOverArea(area);
      } else {
        setDragOverArea(null);
      }
    },
    [isDragging, updateGhostPosition]
  );

  // Mouse up handler
  const handleMouseUp = useCallback(() => {
    // Clean up ghost element
    if (dragGhostRef.current) {
      document.body.removeChild(dragGhostRef.current);
      dragGhostRef.current = null;
    }

    // Remove dragging styles
    document.querySelectorAll(".dragging-original").forEach((el) => {
      el.classList.remove("dragging-original");
    });

    // Handle drop logic
    if (dragOverArea && draggedItem) {
      const { skill, source } = draggedItem;

      // Move between areas
      if (source !== dragOverArea) {
        // Check max skills limit for selected areas
        if (
          (dragOverArea === EDIT_SKILLS_TYPE.ROLE && selectedSkills1.length >= maxRoleSkills) ||
          (dragOverArea === EDIT_SKILLS_TYPE.CULTURE && selectedSkills2.length >= maxCultureSkills)
        ) {
          toastMessageSuccess(
            `Maximum ${dragOverArea === EDIT_SKILLS_TYPE.ROLE ? maxRoleSkills : maxCultureSkills} skills can be selected in each list`
          );
        } else {
          // Actually move the skill
          if (source === EDIT_SKILLS_TYPE.AVAILABLE && dragOverArea === EDIT_SKILLS_TYPE.ROLE) {
            setAvailableSkills((prev) => prev.filter((s) => s.id !== skill.id));
            setSelectedSkills1((prev) => {
              const newSkills = [...prev];
              newSkills.splice(dropIndex, 0, skill);
              return newSkills;
            });
            setHasUnsavedChanges(true);
          } else if (source === EDIT_SKILLS_TYPE.AVAILABLE && dragOverArea === EDIT_SKILLS_TYPE.CULTURE) {
            setAvailableSkills((prev) => prev.filter((s) => s.id !== skill.id));
            setSelectedSkills2((prev) => {
              const newSkills = [...prev];
              newSkills.splice(dropIndex, 0, skill);
              return newSkills;
            });
            setHasUnsavedChanges(true);
          } else if (source === EDIT_SKILLS_TYPE.ROLE && dragOverArea === EDIT_SKILLS_TYPE.AVAILABLE) {
            setSelectedSkills1((prev) => prev.filter((s) => s.id !== skill.id));
            setAvailableSkills((prev) => {
              const newSkills = [...prev];
              newSkills.splice(dropIndex, 0, skill);
              return newSkills.sort((a, b) => a.name.localeCompare(b.name));
            });
            setHasUnsavedChanges(true);
          } else if (source === EDIT_SKILLS_TYPE.CULTURE && dragOverArea === EDIT_SKILLS_TYPE.AVAILABLE) {
            setSelectedSkills2((prev) => prev.filter((s) => s.id !== skill.id));
            setAvailableSkills((prev) => {
              const newSkills = [...prev];
              newSkills.splice(dropIndex, 0, skill);
              return newSkills.sort((a, b) => a.name.localeCompare(b.name));
            });
            setHasUnsavedChanges(true);
          } else if (source === EDIT_SKILLS_TYPE.ROLE && dragOverArea === EDIT_SKILLS_TYPE.CULTURE) {
            setSelectedSkills1((prev) => prev.filter((s) => s.id !== skill.id));
            setSelectedSkills2((prev) => {
              const newSkills = [...prev];
              newSkills.splice(dropIndex, 0, skill);
              return newSkills;
            });
            setHasUnsavedChanges(true);
          } else if (source === EDIT_SKILLS_TYPE.CULTURE && dragOverArea === EDIT_SKILLS_TYPE.ROLE) {
            setSelectedSkills2((prev) => prev.filter((s) => s.id !== skill.id));
            setSelectedSkills1((prev) => {
              const newSkills = [...prev];
              newSkills.splice(dropIndex, 0, skill);
              return newSkills;
            });
            setHasUnsavedChanges(true);
          }
        }
      }
    }

    // Reset states
    setIsDragging(false);
    setDraggedItem(null);
    setDragOverArea(null);
    setDropIndex(0);

    // Cancel animation frame
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  }, [draggedItem, dragOverArea, dropIndex, selectedSkills1, selectedSkills2, maxRoleSkills, maxCultureSkills]);

  // Add global event listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (dragGhostRef.current) {
        document.body.removeChild(dragGhostRef.current);
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  const handleSave = useCallback(async () => {
    if (!hasUnsavedChanges) return;

    setIsLoading(true);
    try {
      // Prepare skills data for saving
      const roleSkillsToSave: ISkillData[] = selectedSkills1.map((skill) => ({
        id: String(skill.id),
        name: skill.name,
        description: skill.description,
      }));

      const cultureSkillsToSave: ISkillData[] = selectedSkills2.map((skill) => ({
        id: String(skill.id),
        name: skill.name,
        description: skill.description,
      }));

      // Validate skill counts
      if (roleSkillsToSave.length !== maxRoleSkills) {
        toastMessageSuccess(`Please select exactly ${maxRoleSkills} role specific skills. You have selected ${roleSkillsToSave.length}.`);
        setIsLoading(false);
        return;
      }

      if (cultureSkillsToSave.length !== maxCultureSkills) {
        toastMessageSuccess(`Please select exactly ${maxCultureSkills} culture specific skills. You have selected ${cultureSkillsToSave.length}.`);
        setIsLoading(false);
        return;
      }

      // Save to Redux
      dispatch(
        setSkillsData({
          roleSpecificSkills: roleSkillsToSave,
          cultureSpecificSkills: cultureSkillsToSave,
        })
      );

      console.log("Saving skills:", { roleSkillsToSave, cultureSkillsToSave });

      // Navigate to next page
      router.push(ROUTES.JOBS.ROLE_BASED_SKILLS);
    } catch (error) {
      console.error("Failed to save skills:", error);
      toastMessageError("Failed to save skills. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [hasUnsavedChanges, selectedSkills1, selectedSkills2, maxRoleSkills, maxCultureSkills, dispatch, router]);

  // Skill card component
  const SkillCard: React.FC<{ skill: ISkill; source: string; showAddButton?: boolean }> = ({ skill, source }) => (
    <div
      data-skill-id={skill.id}
      onMouseDown={(e) => handleDragStart(e, skill, source)}
      className="drag-skill-card  mb-3"
      style={{ userSelect: "none", cursor: "grab" }}
    >
      <div className="head">
        <h3>{skill.name}</h3>
      </div>
      <div className="skill-content">
        <p>{skill.description}</p>
      </div>
    </div>
  );

  // Initialize skills from dynamic data
  useEffect(() => {
    // Add debugging to see what data we're receiving
    console.log("Debug - skillCategories:", skillCategories);
    console.log("Debug - roleSpecificSkills:", roleSpecificSkills);
    console.log("Debug - cultureSpecificSkills:", cultureSpecificSkills);

    if (skillCategories && skillCategories.length > 0) {
      const convertedSkills = convertToSkillFormat(skillCategories);
      console.log("Debug - convertedSkills:", convertedSkills);

      // Mark already selected skills
      const roleSkillIds = roleSpecificSkills.map((skill) => parseInt(skill.id || "0"));
      const cultureSkillIds = cultureSpecificSkills.map((skill) => parseInt(skill.id || "0"));

      const availableSkillsList: ISkill[] = [];
      const preSelectedRole: ISkill[] = [];
      const preSelectedCulture: ISkill[] = [];

      convertedSkills.forEach((skill) => {
        if (roleSkillIds.includes(skill.id)) {
          skill.isAlreadySelected = true;
          preSelectedRole.push(skill);
        } else if (cultureSkillIds.includes(skill.id)) {
          skill.isAlreadySelected = true;
          preSelectedCulture.push(skill);
        } else {
          availableSkillsList.push(skill);
        }
      });

      console.log("Debug - availableSkillsList:", availableSkillsList);
      console.log("Debug - preSelectedRole:", preSelectedRole);
      console.log("Debug - preSelectedCulture:", preSelectedCulture);

      setAvailableSkills(availableSkillsList);
      setSelectedSkills1(preSelectedRole);
      setSelectedSkills2(preSelectedCulture);
    }
  }, [skillCategories, roleSpecificSkills, cultureSpecificSkills, convertToSkillFormat]);
  console.log("category==========>", categories);

  /**
   * Fetches skills data from the API and updates Redux store
   *
   * @async
   * @function fetchSkillsData
   * @returns {Promise<void>}
   * @throws {Error} If API request fails
   * @description Makes an API call to fetch all skills data and updates Redux store
   *              with the response. Handles both success and error cases.
   */
  const fetchSkillsData = useCallback(async () => {
    try {
      dispatch(fetchSkillsStart());
      const response = await getAllSkills();
      if (response.data && response.data.success) {
        dispatch(fetchSkillsSuccess(response.data.data as ISkillCategory[]));
      } else {
        dispatch(fetchSkillsFailure(response.data?.message as string));
      }
    } catch (error: unknown) {
      console.error(t("error_fetching_skills"), error);
      dispatch(fetchSkillsFailure((error as Error)?.message || t("error_fetching_skills")));
    }
  }, [dispatch, t]);

  // Add CSS animation styles for drag effect
  useEffect(() => {
    // Create style element for drag animation
    const style = document.createElement("style");
    style.textContent = `
      @keyframes dragPulse {
        0% { transform: scale(1.05); }
        50% { transform: scale(1.07); }
        100% { transform: scale(1.05); }
      }
      .drag-ghost {
        transition: none !important;
        animation: dragPulse 1.5s ease-in-out infinite;
        cursor: grabbing !important;
      }
      .dragging-original {
        opacity: 0.3;
        outline: 2px dashed #5a9bef;
      }
    `;
    document.head.appendChild(style);

    // Remove on unmount
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    // Trigger data load on component mount
    fetchSkillsData();
  }, [fetchSkillsData]);

  return (
    <div className={style.job_page} ref={containerRef}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                <BackArrowIcon
                  onClick={() => {
                    router.back();
                  }}
                />
                {t("top_performance_based_skills")}
                <span>{jobDetails?.title || "Job Title"}</span>
              </h2>
            </div>
            <p className="description">
              {t("performance_skills_part1")} <span>{jobDetails?.title || "Job Title"}</span> {t("performance_skills_part2")}
              <strong className="color-primary">{t("performance_skills_part3")}</strong>.
            </p>
          </div>
        </div>

        <div className="inner-section performance-based-skill career-based-skills">
          <h3 className={style.inner_heading}>
            {t("top")} <span>{t("performance_based_skills")}</span>
          </h3>

          {/* Desktop layout with horizontal arrangement  */}
          <div className="row g-4 justify-content-between">
            {/* Available Skills */}
            <div className="col-lg-5">
              <div data-drop-area={EDIT_SKILLS_TYPE.AVAILABLE} className="drag-card">
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <h5 className="tittle">Available Skills</h5>

                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="form-select-drodown"
                    style={{ fontSize: "14px", width: "auto" }}
                  >
                    {categories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="big-card-hight">
                  {filteredAvailableSkills.map((skill) => (
                    <SkillCard key={`available-${skill.id}`} skill={skill} source={EDIT_SKILLS_TYPE.AVAILABLE} showAddButton={true} />
                  ))}
                  {filteredAvailableSkills.length === 0 && (
                    <div className="drag-here">
                      <p>No skills available for this category</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Middle section with drag-and-drop instruction */}
            <div className="col-lg-2 d-flex align-items-center justify-content-center">
              <div className="drag-drop-icon">
                <p className="drag-drop-text">
                  Drag & Drop <br />
                  From Left To Right
                </p>
                <DualSideIcon className="m-auto d-block" />
              </div>
            </div>

            {/* Right side column with role and culture skills stacked */}
            <div className="col-lg-5">
              {/* Role Based Skills */}
              <div data-drop-area={EDIT_SKILLS_TYPE.ROLE} className="drag-card mb-5 ">
                <h5 className="tittle text-center">
                  Role Based Skills ({selectedSkills1.length}/{maxRoleSkills})
                </h5>
                <div className="small-card-hight">
                  {selectedSkills1.map((skill) => (
                    <SkillCard key={`selected1-${skill.id}`} skill={skill} source={EDIT_SKILLS_TYPE.ROLE} />
                  ))}
                  {selectedSkills1.length === 0 && (
                    <div className="drag-here">
                      <p>Drag skills here</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Culture Based Skills */}
              <div data-drop-area={EDIT_SKILLS_TYPE.CULTURE} className="drag-card ">
                <h5 className="tittle text-center">
                  Culture Based Skills ({selectedSkills2.length}/{maxCultureSkills} ){" "}
                </h5>
                <div className="small-card-hight">
                  {selectedSkills2.map((skill) => (
                    <SkillCard key={`selected2-${skill.id}`} skill={skill} source={EDIT_SKILLS_TYPE.CULTURE} />
                  ))}
                  {selectedSkills2.length === 0 && (
                    <div className="drag-here">
                      <p>Drag skills here</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="button-align py-5">
          <Button
            className="primary-btn rounded-md"
            onClick={handleSave}
            disabled={isLoading || !hasUnsavedChanges || selectedSkills1.length + selectedSkills2.length === 0}
          >
            {isLoading ? (
              <>
                <Loader /> Saving...
              </>
            ) : (
              `${t("save_and_next")} (${selectedSkills1.length + selectedSkills2.length})`
            )}
          </Button>
          <Button className="dark-outline-btn rounded-md" onClick={() => router.back()} disabled={isLoading}>
            {t("cancel")}
          </Button>
        </div>
      </div>
    </div>
  );
}

export default PerformanceBasedSkills;
