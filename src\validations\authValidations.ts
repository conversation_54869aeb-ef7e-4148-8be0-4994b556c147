import {
  BRANCH_CODE_REGEX,
  BRANCH_NAME_REGEX,
  ORGANIZATION_CODE_REGEX,
  ORGANIZATION_NAME_REGEX,
  PASSWORD_REGEX,
  TIN_NUMBER_REGEX,
  WEBSITE_URL_REGEX,
} from "@/constants/commonConstants";
import * as yup from "yup";

export const EMAIL_REGEX =
  /^[a-zA-Z0-9](?:[a-zA-Z0-9._%+-]{0,30}[a-zA-Z0-9])?@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,30}[a-zA-Z0-9])?\.){1,2}[a-zA-Z]{2,6}$/;
// Only letters, numbers, and spaces allowed with length constraints (min 3, max 50)
export const NAME_REGEX = /^[a-zA-Z\s]{3,50}$/;

export const loginValidation = (translation: (key: string) => string) =>
  yup
    .object()
    .concat(forgotPasswordValidation(translation))
    .shape({
      password: yup.string().required(translation("pass_req")),
    });

export const resetPasswordValidation = (translation: (key: string) => string) =>
  yup.object().shape({
    new_password: yup.string().required(translation("pass_req")).matches(PASSWORD_REGEX, translation("pass_val")),
    confirm_password: yup
      .string()
      .required(translation("confirm_pass_req"))
      .oneOf([yup.ref("new_password")], translation("pass_not_match")),
  });

export const verifyOTPValidation = (translation: (key: string) => string) =>
  yup.object().shape({
    otp: yup
      .number()
      .typeError(translation("valid_otp"))
      .test("len", translation("valid_otp"), (val) => val?.toString().length === 4)
      .required(translation("otp_req")),
  });

export const forgotPasswordValidation = (translation: (key: string) => string) => EmailValidation(translation);

export const EmailValidation = (translation: (key: string) => string) =>
  yup.object().shape({
    email: yup
      .string()
      .trim()
      .required(translation("email_req_m"))
      .email(translation("email_val_msg"))
      .matches(EMAIL_REGEX, translation("email_val_msg")),
  });

export const signUpValidationSchema = (translation: (key: string) => string) =>
  yup.object().shape({
    firstName: yup
      .string()
      .trim()
      .required(translation("firstname_req"))
      .min(3, translation("min_first_name_length"))
      .max(50, translation("max_first_name_length"))
      .matches(NAME_REGEX, translation("invalid_first_name")),
    lastName: yup
      .string()
      .trim()
      .required(translation("lastname_req"))
      .min(3, translation("min_last_name_length"))
      .max(50, translation("max_last_name_length"))
      .matches(NAME_REGEX, translation("invalid_last_name")),
    email: yup
      .string()
      .trim()
      .required(translation("email_req"))
      .email(translation("email_val_msg"))
      .matches(EMAIL_REGEX, translation("email_val_msg")),
    password: yup.string().required(translation("pass_req")).matches(PASSWORD_REGEX, translation("pass_val")),
    organizationCode: yup
      .string()
      .trim()
      .required(translation("organization_code_req"))
      .min(6, translation("min_org_length"))
      .max(10, translation("max_org_length"))
      .matches(ORGANIZATION_CODE_REGEX, translation("invalid_org_code")),
    location: yup
      .string()
      .trim()
      .required(translation("location_req"))
      .min(3, translation("min_length"))
      .max(50, translation("max_length"))
      .matches(NAME_REGEX, translation("invalid_location_name")),
    organizationName: yup
      .string()
      .trim()
      .required(translation("organization_name_req"))
      .min(3, translation("min_length"))
      .max(150, translation("max_org_name_length"))
      .matches(ORGANIZATION_NAME_REGEX, translation("invalid_org_name_format")),
    websiteURL: yup
      .string()
      .trim()
      .nullable()
      .optional()
      .test("orgDomain-min-max", translation("min_length"), (val) => !val || val.length === 0 || val.length >= 3)
      .test("orgDomain-max", translation("max_domain_length"), (val) => !val || val.length === 0 || val.length <= 255)
      .matches(WEBSITE_URL_REGEX, translation("invalid_domain_format")),
    branchName: yup
      .string()
      .trim()
      .nullable()
      .optional()
      .test("branchName-format", translation("invalid_branch_name_format"), (val) => !val || val.length === 0 || BRANCH_NAME_REGEX.test(val)),
    branchCode: yup
      .string()
      .trim()
      .nullable()
      .optional()
      .test("branchCode-format", translation("invalid_branch_code_format"), (val) => !val || val.length === 0 || BRANCH_CODE_REGEX.test(val)),
    tinNumber: yup
      .string()
      .trim()
      .nullable()
      .optional()
      .test("tinNumber-format", translation("invalid_tin_format"), (val) => !val || val.length === 0 || TIN_NUMBER_REGEX.test(val)),
  });

export type SignUpFormValues = yup.InferType<ReturnType<typeof signUpValidationSchema>>;
