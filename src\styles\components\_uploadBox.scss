@use "../abstracts" as *;

.upload-card {
  border-radius: 16px;
  border: 1px dashed rgba(51, 51, 51, 0.7);
  background: rgba(51, 51, 51, 0.05);
  text-align: center;
  width: 100%;
  padding: 40px 20px;
  position: relative;
  min-height: 138px;
  input {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 100;
  }
  .upload-box-inner {
    svg {
      width: 42px;
      min-width: 42px;
      height: 42px;
      margin-bottom: 15px;
    }
    p {
      color: $dark;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      opacity: 0.7;

      &.uploading-message {
        opacity: 1;
        color: $primary;
        font-weight: $semiBold;
        transition: opacity 0.3s ease-in-out;
        animation: fadeInOut 0.5s ease-in-out;
      }
    }
  }
  &.upload-card-sm {
    padding: 20px;
  }
}

//UplodedItem
.uploded-item {
  border-radius: 16px;
  border: 1px solid #bdbdbd;
  background-color: $white;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  width: fit-content;
  .item-name {
    display: flex;
    align-items: center;
    gap: 15px;
    svg {
      width: 32px;
      min-width: 32px;
      height: 32px;
    }
    p {
      color: $dark;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
    }
  }
  .delete-item {
    cursor: pointer;
    width: 32px;
    min-width: 32px;
    height: 32px;
  }
  &.upload-card-sm {
    padding: 10px;
    border-radius: 8px;
    margin-top: 10px;
    gap: 15px;

    .item-name {
      gap: 10px;
      svg {
        width: 18px;
        min-width: 18px;
        height: 18px;
      }
      p {
        color: $dark;
        font-size: 14px;
        font-weight: $semiBold;
        margin-bottom: 0;
      }
    }
    .delete-item {
      cursor: pointer;
      width: 20px;
      min-width: 20px;
      height: 20px;
    }
  }
}

.uploaded-item-row-overflow {
  display: flex;
  overflow-x: auto;
  gap: 10px;
  padding-bottom: 10px;
}
// Animation for dynamic uploading messages
@keyframes fadeInOut {
  0% {
    opacity: 0.7;
    transform: translateY(2px);
  }
  50% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
