import Button from "@/components/formElements/Button";
import style from "../../../styles/conductInterview.module.scss";
import AssessmentSummarySwiper from "./AssessmentSummarySwiper";
const FinalAssessmentSummary = () => {
  return (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                Final <span>Assessment Summary</span>
              </h2>
            </div>
          </div>
        </div>
        <div className="inner-section">
          <AssessmentSummarySwiper />
          <div className="row">
            <div className="col-md-12">
              <div className="summary-text-card">
                <h3 className="tittle">Organization</h3>
                <h5 className="sub-tittle">Strengths</h5>
                <ul>
                  <li>
                    <p>Strong note-taking skills (copious notes, uses OneNote and a notebook).</p>
                  </li>
                  <li>
                    <p>Created a handbook for interns, demonstrating initiative and organizational thoughtfulness.</p>
                  </li>
                  <li>
                    <p>Effective use of calendar and email for tracking tasks and communications.</p>
                  </li>
                </ul>
                <h5 className="sub-tittle">Potential Gaps</h5>
                <ul>
                  <li>
                    <p>Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.</p>
                  </li>
                </ul>
                <h5 className="sub-tittle">
                  Probability of Success in This Skill: <span className="color-secondary">80%</span>
                </h5>
                <ul>
                  <li>
                    <p>Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.</p>
                  </li>
                </ul>
              </div>
            </div>
            <div className="col-md-6">
              <div className={`summary-text-card ${style.summary_card_height}`}>
                <h3 className="sub-tittle mt-0">Overall Probability of Success in the Role</h3>
                <h5 className="big-text">80%</h5>
              </div>
            </div>
            <div className="col-md-6">
              <div className={`summary-text-card ${style.summary_card_height}`}>
                <h3 className="sub-tittle mt-0">Summary</h3>
                <p>
                  Brianna demonstrates strong foundational skills, emotional intelligence, and a genuine desire to grow and learn. While she lacks
                  extensive experience, her temperament, curiosity, and EQ make her a great cultural fit. With proper mentorship and structured
                  challenges, she has the potential to excel in the role and contribute positively to our organization.
                </p>
              </div>
            </div>
            <div className="col-md-12">
              <div className="summary-text-card">
                <h3 className="tittle">Recommendations for Development</h3>
                <h5 className="sub-tittle">Structured Mentorship</h5>
                <ul>
                  <li>
                    <p>As a mentor, Ginny can help her refine skills like prioritization, assertiveness, and decision-making.</p>
                  </li>
                </ul>
                <h5 className="sub-tittle">Growth Opportunities</h5>
                <ul>
                  <li>
                    <p>Provide challenging tasks in incremental steps to keep her engaged and advancing.</p>
                  </li>
                </ul>
                <h5 className="sub-tittle">Skill Development</h5>
                <ul>
                  <li>
                    <p>
                      Offer resources and training on advanced organization, communication, and forecasting tools to build her confidence. Use hands
                      on training and real-time feedback to help her learn and excel.
                    </p>
                  </li>
                </ul>
              </div>
            </div>
            {/* this card for last step of final assessment summary page */}
            <div className="col-md-12">
              <div className="summary-text-card">
                <h3 className="tittle">Candidate Review for Operations Admin Role</h3>
                <p>
                  The candidate demonstrates strong communication, problem-solving, and empathy, making them effective in stakeholder engagement and
                  team collaboration. They show good prioritization skills and a commitment to growth but need to improve adaptability to modern
                  organizational tools. With guidance in task management, they have solid potential for success in the role.
                </p>
              </div>
            </div>
          </div>
        </div>
        <Button className="primary-btn rounded-md">Continue</Button>
      </div>
    </div>
  );
};

export default FinalAssessmentSummary;
