import React from "react";

function FolderColorIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="184" height="126" viewBox="0 0 184 126" fill="none" className={className}>
      <g filter="url(#filter0_ddi_10045_8703)">
        <path
          d="M5 10.4446C5 5.96629 8.63036 2.33594 13.1086 2.33594H38.6011C41.6724 2.33594 44.4801 4.0712 45.8537 6.81827L51.0244 17.1598C52.398 19.9069 55.2057 21.6422 58.277 21.6422H170.648C175.126 21.6422 178.756 25.2725 178.756 29.7508V110.065C178.756 114.543 175.126 118.173 170.648 118.173H13.1086C8.63036 118.173 5 114.543 5 110.065V10.4446Z"
          fill="#436EB6"
        />
      </g>
      <defs>
        <filter
          id="filter0_ddi_10045_8703"
          x="0.366501"
          y="-13.8813"
          width="183.023"
          height="139.003"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
          <feOffset dy="2.31675" />
          <feGaussianBlur stdDeviation="2.31675" />
          <feColorMatrix type="matrix" values="0 0 0 0 0.172549 0 0 0 0 0.168627 0 0 0 0 0.164706 0 0 0 0.1 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10045_8703" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
          <feOffset dx="1.15837" dy="1.15837" />
          <feGaussianBlur stdDeviation="0.579187" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
          <feBlend mode="normal" in2="effect1_dropShadow_10045_8703" result="effect2_dropShadow_10045_8703" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_10045_8703" result="shape" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
          <feOffset dy="-16.2172" />
          <feGaussianBlur stdDeviation="12.1629" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
          <feBlend mode="normal" in2="shape" result="effect3_innerShadow_10045_8703" />
        </filter>
      </defs>
    </svg>
  );
}

export default FolderColorIcon;
