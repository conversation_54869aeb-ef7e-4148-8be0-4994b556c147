/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { DateSelectArg, DatesSetArg } from "@fullcalendar/core";
import { EventImpl } from "@fullcalendar/core/internal";
import { useTranslations } from "next-intl";
import { debounce } from "lodash";
import { yupResolver } from "@hookform/resolvers/yup";

import { formatTimeForInput, toastMessageError, toastMessageSuccess } from "@/utils/helper";
import CalenderEventModal from "@/components/commonModals/CalendarEventModal";
import style from "../../../styles/conductInterview.module.scss";
import calendarStyles from "../../../styles/calendar.module.scss";
import { getInterviewers, getJobList, getMyInterviews, updateOrScheduleInterview } from "@/services/interviewServices";
import { scheduleInterviewValidation } from "@/validations/interviewValidations";
import { IGetInterviewersResponse, IGetInterviewsResponse, IGetJobListResponse, ScheduleInterviewFormValues } from "@/interfaces/interviewInterfaces";
import InterviewDetailModal from "@/components/commonModals/InterviewDetailModal";
import CommonCalendar from "@/components/commonComponent/CommonCalendar";
import { PERMISSION, ScheduleInterviewFormSubmissionType } from "@/constants/commonConstants";
import { useHasPermission } from "@/utils/permission";
import toast from "react-hot-toast";
import { useTranslate } from "@/utils/translationUtils";
import useDebounce from "@/hooks/useDebounce";
import CalenderLoader from "@/components/loader/CalenderLoader";

export const defaultValues = {
  eventTitle: "",
  date: "",
  startTime: "",
  endTime: "",
  description: "",
  interviewer: undefined,
  jobId: "",
  jobTitle: undefined,
  interviewType: "",
  candidate: undefined,
};

const ScheduleInterviewFromCalendar = () => {
  const t = useTranslations();
  const translate = useTranslate();
  const initialFetchCompleted = useRef(false);

  const [interviews, setInterviews] = useState<IGetInterviewsResponse[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [interviewers, setInterviewers] = useState<IGetInterviewersResponse[]>([]);
  const [monthYear, setMonthYear] = useState<string>(`${String(new Date().getMonth() + 1).padStart(2, "0")}-${new Date().getFullYear()}`);
  const debouncedMonthYear = useDebounce(monthYear, 500); // Debounce for 500ms
  const [loader, setLoader] = useState(false);
  const [jobLoader, setJobLoader] = useState(false);
  const [calendarLoading, setCalendarLoading] = useState(false);
  const [fileUrls, setFileUrls] = useState<string[]>([]);
  const [interviewInfo, setInterviewInfo] = useState<IGetInterviewsResponse | null>(null);
  const [jobs, setJobs] = useState<IGetJobListResponse[]>([]);
  const [formType, setFormType] = useState<(typeof ScheduleInterviewFormSubmissionType)[keyof typeof ScheduleInterviewFormSubmissionType]>(
    ScheduleInterviewFormSubmissionType.SCHEDULE
  );

  const currentFileArrayLengthRef = useRef(fileUrls ? fileUrls?.length : 0);

  const hasScheduleInterviewPermission = useHasPermission(PERMISSION.SCHEDULE_CONDUCT_INTERVIEWS);

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    setError,
    formState: { errors },
    getValues,
  } = useForm({
    resolver: yupResolver(scheduleInterviewValidation(translate)),
  });

  console.log("watch==", watch());

  console.log("fileUrls==", fileUrls);
  console.log("currentFileArrayLengthRef.current==", currentFileArrayLengthRef.current);

  const getAllInterviews = async (monthYear: string, skipLoading: boolean = false) => {
    if (!skipLoading) {
      setCalendarLoading(true);
    }
    try {
      const result = await getMyInterviews(monthYear);

      console.log("result", result);
      if (result?.data?.success) {
        const events = result?.data?.data;
        setInterviews(events);
      }
    } catch (error) {
      console.log(error);
    } finally {
      if (!skipLoading) {
        setCalendarLoading(false);
      }
    }
  };

  const onSubmit = async (data: ScheduleInterviewFormValues) => {
    try {
      if (!hasScheduleInterviewPermission) {
        toastMessageError(t("you_dont_have_permission_to_schedule_interview"));
        return;
      }
      setLoading(true);
      const { eventTitle, date, startTime, endTime, description, jobTitle, candidate, interviewer, interviewType } = data;
      console.log("inside onsubmit");

      const startDateTime = new Date(`${date}T${startTime}`);
      const endDateTime = new Date(`${date}T${endTime}`);

      // Convert to timestamp (milliseconds since epoch)
      const startTimestamp = startDateTime.getTime();
      const endTimestamp = endDateTime.getTime();
      const scheduleAtTimestamp = new Date().getTime();

      const updatePayload = {
        title: eventTitle,
        interviewerId: +interviewer,
        scheduleAt: scheduleAtTimestamp,
        startTime: startTimestamp,
        endTime: endTimestamp,
        description: description ?? "",
        roundType: interviewType,
        jobApplicationId: +candidate,
        interviewId: +interviewInfo?.id!,
        fileUrlArray: JSON.stringify(fileUrls),
      };

      const payload = {
        ...updatePayload,
        interviewId: undefined,
        jobId: +jobTitle,
      };

      console.log("payload======>>>>", payload);

      const result = await updateOrScheduleInterview(formType === ScheduleInterviewFormSubmissionType.SCHEDULE ? payload : updatePayload);

      if (result?.data?.success) {
        toastMessageSuccess(t(result?.data?.message));
        setIsModalOpen(false);
        reset();
        setFileUrls([]);
        currentFileArrayLengthRef.current = 0;
        getAllInterviews(monthYear);
        setLoading(false);
      } else {
        setLoading(false);
        toastMessageError(t(result?.data?.message));
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // this function automatically called when calender renders
  const handleDatesSet = (info: DatesSetArg) => {
    console.log("Dates set:", info, info.view.type);

    // Get start date and add 7 days to ensure we're in the current month view
    const startDate = new Date(info.start);
    const adjustedDate = new Date(startDate);
    adjustedDate.setDate(startDate.getDate() + 7);

    // Format as MM-YYYY
    const month = String(adjustedDate.getMonth() + 1).padStart(2, "0"); // +1 because months are 0-indexed
    const year = adjustedDate.getFullYear();
    const formattedDate = `${month}-${year}`;

    console.log(">>>>>>>>>>>>>>>formattedDate", formattedDate);
    console.log(">>>>>>>>>>>debouncedMonthYear", debouncedMonthYear);
    console.log(">>>>>>>>>>>>initialFetchCompleted.current", initialFetchCompleted.current);

    // Trigger state update for the current month only if it's different from the debounced value
    if (formattedDate !== debouncedMonthYear || !initialFetchCompleted.current) {
      setMonthYear(formattedDate); // This will trigger the debounce
      setInterviews([]); // Clear the current interviews
      initialFetchCompleted.current = true;
    }
  };

  // when click on scheduled interview event it will show the data on the interview detail modal
  const handleEventClick = ({ event }: { event: EventImpl }) => {
    console.log("Event clicked:", event);

    const interviewInfoObj = interviews.find((interview) => Number(interview.id) === Number(event.id)) ?? null;

    console.log(interviews.length, "interviewInfoObj", interviewInfoObj);

    if (!interviewInfoObj?.skillQuestionsGenerated) {
      getAllInterviews(monthYear, true);
    }

    setInterviewInfo(interviewInfoObj);
    const filesUrls = interviewInfoObj ? JSON.parse(interviewInfoObj?.attachments)?.fileUrls : [];
    setFileUrls(filesUrls);
    setIsViewModalOpen(true);
  };

  // function to select the date from calender
  const handleOnSelect = (info: DateSelectArg) => {
    console.log(new Date().getTime(), "Event selected:", new Date(info.start).getTime());
    toast.dismiss();
    if (!hasScheduleInterviewPermission) {
      toast.dismiss();
      console.log("inside if", !hasScheduleInterviewPermission);
      toastMessageError(t("you_dont_have_permission_to_schedule_interview"));
      return;
    }
    // Parse the selected date information
    const startDate = new Date(info.start);
    const endDate = new Date(info.end);

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDay = new Date(startDate);
    selectedDay.setHours(0, 0, 0, 0);

    if (selectedDay < today) {
      toastMessageError(t("cannot_schedule_interview_past"));
      return;
    }
    // Format date as YYYY-MM-DD for date input
    const formattedDate = startDate.toLocaleDateString("en-CA");
    console.log("formattedDate", formattedDate);

    const startTime = formatTimeForInput(startDate);
    const endTime = formatTimeForInput(endDate);

    setValue("date", formattedDate);
    setValue("startTime", startTime);
    setValue("endTime", endTime);
    setValue("interviewType", "");
    setIsModalOpen(true);
    // getInterviewersList("");
    getJobs("");
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setFormType(ScheduleInterviewFormSubmissionType.SCHEDULE);
    reset(defaultValues);
  };

  const watchedJobTitle = watch("jobTitle");

  // function to fetch interviewers list
  const getInterviewersList = useCallback(
    async (searchString: string, preserveCurrentInterviewer = false) => {
      if (!watchedJobTitle) return;

      setLoader(true);
      try {
        const response = await getInterviewers(searchString, watchedJobTitle?.toString());

        if (response?.data?.success) {
          // Format interviewer options to include department name in the label for better context
          const formattedInterviewers = response?.data?.data.map((interviewer: IGetInterviewersResponse & { departmentName?: string }) => ({
            ...interviewer,
            label: interviewer.departmentName ? `${interviewer.label} (${interviewer.departmentName})` : interviewer.label,
          }));
          // In edit mode, ensure that the previously selected interviewer appears in the dropdown
          // even if they don't match the current department/job filter criteria
          if (preserveCurrentInterviewer && formType === ScheduleInterviewFormSubmissionType.UPDATE && interviewInfo) {
            // Check if current interviewer is already in the list from API response
            const currentInterviewerId = interviewInfo?.interviewerId;
            const currentInterviewerName = interviewInfo?.interviewerName;
            const currentDepartmentName = interviewInfo?.departmentName;
            const interviewerExists = formattedInterviewers.some(
              (i: IGetInterviewersResponse & { departmentName?: string }) => i.value === currentInterviewerId
            );

            // If the current interviewer isn't in the filtered results, add them to the top of the list
            // This ensures continuity when editing interviews and prevents data loss
            if (!interviewerExists && currentInterviewerId && currentInterviewerName) {
              const updatedInterviewers = [
                {
                  label: currentDepartmentName ? `${currentInterviewerName} (${currentDepartmentName})` : currentInterviewerName,
                  value: currentInterviewerId,
                  departmentName: currentDepartmentName,
                },
                ...formattedInterviewers,
              ];
              setInterviewers(updatedInterviewers);
              return;
            }
          }
          setInterviewers(formattedInterviewers);
        }
      } catch (error) {
        console.error("Error fetching interviewers:", error);
      } finally {
        setLoader(false);
      }
    },
    [watchedJobTitle, formType, interviewInfo]
  );

  useEffect(() => {
    if (debouncedMonthYear) {
      // Trigger the function to update or fetch interviews for the new month
      getAllInterviews(debouncedMonthYear);
    }
  }, [debouncedMonthYear]);

  useEffect(() => {
    getInterviewersList("", formType === ScheduleInterviewFormSubmissionType.UPDATE);
  }, [watchedJobTitle]);

  // console.log("watch", watch("jobTitle"));

  const handleSearchInputChange = (event: string) => {
    const searchString = event.trim();
    console.log("searchString", searchString);
    getInterviewersList(searchString);
  };

  const debouncedHandleSearchInputChange = debounce(handleSearchInputChange, 1000);

  const getJobs = useCallback(async (searchString: string) => {
    setJobLoader(true);
    try {
      const response = await getJobList(searchString);

      if (response?.data?.success) {
        setJobs(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching interviewers:", error);
    } finally {
      setJobLoader(false);
    }
  }, []);

  const handleJobSearchInputChange = (event: string) => {
    const searchString = event.trim();
    console.log("searchString", searchString);
    getJobs(searchString);
  };

  const debouncedHandleJobSearchInputChange = debounce(handleJobSearchInputChange, 1000);

  // function to edit or update the interview
  const onHandleEdit = () => {
    console.log("inside edit");
    setIsViewModalOpen(false);
    setFormType(ScheduleInterviewFormSubmissionType.UPDATE);
    setIsModalOpen(true);

    const startDate = interviewInfo?.start ? new Date(interviewInfo.start) : new Date();
    const endDate = interviewInfo?.end ? new Date(interviewInfo.end) : new Date();

    const startTime = formatTimeForInput(startDate);
    const endTime = formatTimeForInput(endDate);

    // Add the current interviewer to options list to ensure they always appear in the dropdown
    // This is necessary because when editing, the current interviewer must be in the options
    // regardless of department/job filtering applied by the API
    if (interviewInfo?.interviewerId && interviewInfo?.interviewerName) {
      const currentInterviewer = {
        label: interviewInfo.departmentName ? `${interviewInfo.interviewerName} (${interviewInfo.departmentName})` : interviewInfo.interviewerName,
        value: interviewInfo.interviewerId,
        departmentName: interviewInfo.departmentName,
      };

      // Ensure we don't have duplicates
      const interviewerExists = interviewers.some((i) => i.value === currentInterviewer.value);
      if (!interviewerExists) {
        setInterviewers((prev) => [currentInterviewer, ...prev]);
      }
    }

    reset({
      eventTitle: interviewInfo?.title,
      date: startDate.toLocaleDateString("en-CA"),
      startTime,
      endTime,
      description: interviewInfo?.description,
      interviewer: interviewInfo?.interviewerId,
      jobId: interviewInfo?.jobUniqueId,
      jobTitle: interviewInfo?.jobId,
      interviewType: interviewInfo?.roundType,
      candidate: interviewInfo?.jobApplicationId,
    });

    getInterviewersList("", true);
  };

  const onHandleViewModelClose = () => {
    setInterviewInfo(null);
    setFileUrls([]);
    setIsViewModalOpen(false);
    reset(defaultValues);
  };

  return (
    <div className={`${style.conduct_interview_page} pb-0`}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>{t("calendar")}</h2>
            </div>
          </div>
        </div>
        <div className="inner-section">
          <div className="position-relative">
            {/* {calendarLoading && <CalendarSkeleton/>} */}
            {calendarLoading && <CalenderLoader show={calendarLoading} />}
            <div className={calendarStyles["calendar-container"]}>
              <CommonCalendar
                handleDatesSet={handleDatesSet}
                handleOnSelect={handleOnSelect}
                interviews={interviews}
                handleEventClick={handleEventClick}
                calendarLoading={calendarLoading}
              />
            </div>
          </div>
          {isModalOpen ? (
            <CalenderEventModal
              control={control}
              onClose={handleModalClose}
              handleSubmit={handleSubmit((data) => {
                console.log("data", data);
                return onSubmit(data as ScheduleInterviewFormValues);
              })}
              setFileUrls={setFileUrls}
              fileUrls={fileUrls}
              loading={loading}
              jobs={jobs}
              jobLoader={jobLoader}
              errors={errors}
              currentFileArrayLengthRef={currentFileArrayLengthRef}
              interviewers={interviewers}
              loader={loader}
              setValue={setValue}
              getValues={getValues}
              setError={setError}
              interviewInfo={interviewInfo}
              setJobs={setJobs}
              formType={formType}
              debouncedHandleSearchInputChange={debouncedHandleSearchInputChange}
              debouncedHandleJobSearchInputChange={debouncedHandleJobSearchInputChange}
            />
          ) : null}
          {isViewModalOpen ? (
            <InterviewDetailModal onEdit={onHandleEdit} onClose={onHandleViewModelClose} interviewInfo={interviewInfo} attachments={fileUrls} />
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default ScheduleInterviewFromCalendar;
