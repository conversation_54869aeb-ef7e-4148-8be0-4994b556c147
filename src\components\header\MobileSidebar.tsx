import React, { useState } from "react";
import NavSettingsIcon from "../svgComponents/NavSettingsIcon";
import NavJobsIcon from "../svgComponents/NavJobsIcon";
import NavCalendarIcon from "../svgComponents/NavCalendarIcon";
import NavCandidatesIcon from "../svgComponents/NavCandidatesIcon";
import NavHomeIcon from "../svgComponents/NavHomeIcon";
import { useRouter } from "next/navigation";
import ROUTES from "@/constants/routes";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import NotificationIcon from "../svgComponents/Notification";
import LogoutIcon from "../svgComponents/LogoutIcon";
import { useSelector } from "react-redux";
import { selectProfileData, selectRole } from "@/redux/slices/authSlice";
import { IUserData } from "@/interfaces/authInterfaces";
import Image from "next/image";
import User from "../../../public/assets/images/user.png";
import downArrow from "../../../public/assets/images/down-arrow.svg";
import ProfileIcon from "../svgComponents/ProfileIcon";
import { useTranslations } from "next-intl";
import { PERMISSION } from "@/constants/commonConstants";
import { useHasPermission } from "@/utils/permission";
import HistoryIcon from "../svgComponents/HistoryIcon";
import { logout } from "@/utils/helper";

const MobileSidebar = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
  const navigate = useRouter();
  const [subLink, setSubLink] = useState(false);
  const [history, setHistory] = useState(false);
  const userProfile: IUserData | null = useSelector(selectProfileData);
  const t = useTranslations("header");
  const userRole = useSelector(selectRole);

  // Permission checks
  const hasCreateNewRolePermission = useHasPermission(PERMISSION.CREATE_NEW_ROLE);
  const hasManageUserPermissionsPermission = useHasPermission(PERMISSION.MANAGE_USER_PERMISSIONS);
  const hasCreateNewDepartmentPermission = useHasPermission(PERMISSION.CREATE_NEW_DEPARTMENT);
  const hasAddEmployeePermission = useHasPermission(PERMISSION.ADD_EMPLOYEE);
  const hasViewHiredCandidatesPermission = useHasPermission(PERMISSION.VIEW_HIRED_CANDIDATES);
  const hasHireCandidatePermission = useHasPermission(PERMISSION.HIRE_CANDIDATE);
  const hasCandidatesPermission = hasViewHiredCandidatesPermission || hasHireCandidatePermission;

  // History dropdown permissions
  const hasViewAuditLogsPermission = useHasPermission(PERMISSION.VIEW_AUDIT_LOGS_UPCOMING);
  const hasArchiveRestoreCandidatesPermission = useHasPermission(PERMISSION.ARCHIVE_RESTORE_CANDIDATES);
  const hasArchiveRestoreJobsPermission = useHasPermission(PERMISSION.ARCHIVE_RESTORE_JOB_POSTS);
  const hasArchivePermission = hasArchiveRestoreCandidatesPermission || hasArchiveRestoreJobsPermission;
  const hasAnyHistoryPermission = hasViewAuditLogsPermission || hasArchivePermission;

  // Settings dropdown permissions
  const hasAnySettingsPermission =
    hasCreateNewRolePermission || hasManageUserPermissionsPermission || hasCreateNewDepartmentPermission || hasAddEmployeePermission;

  const onHandleLogout = () => {
    logout();
    onClose();
  };

  return (
    <>
      <div className={`sidebar ${isOpen ? "open" : ""}`}>
        <div className="sidebar-header">
          <div className="circle_img">
            <Image src={userProfile?.image || User} alt="Profile" className="sidebar-profile" width={100} height={100} />
            <div>
              <h5 id="dropdown-user-name">{`${userProfile?.first_name}`}</h5>
              <span>{userRole?.roleName && userRole.roleName.length > 10 ? `${userRole.roleName.substring(0, 10)}...` : userRole?.roleName}</span>
            </div>
          </div>

          <Button onClick={onClose} className="clear-btn p-0">
            <ModalCloseIcon />
          </Button>
        </div>
        <div className="sidebar-menu">
          <li
            onClick={() => {
              navigate.push(ROUTES.DASHBOARD);
              onClose();
            }}
          >
            <NavHomeIcon /> Dashboard
          </li>
          {hasCandidatesPermission && (
            <li
              onClick={() => {
                navigate.push(ROUTES.SCREEN_RESUME.HIRED_CANDIDATES);
                onClose();
              }}
            >
              <NavCandidatesIcon /> Hired
            </li>
          )}
          <li
            onClick={() => {
              navigate.push(ROUTES.INTERVIEW.CALENDAR);
              onClose();
            }}
          >
            <NavCalendarIcon /> Calendar
          </li>
          <li
            onClick={() => {
              navigate.push(ROUTES.JOBS.ACTIVE_JOBS);
              onClose();
            }}
          >
            <NavJobsIcon /> Active Jobs
          </li>
          {hasAnySettingsPermission && (
            <li className="sub-menu-bar" onClick={() => setSubLink(!subLink)}>
              <div className="sub-menu-list">
                <NavSettingsIcon />{" "}
                <span>
                  Settings{" "}
                  <Image src={downArrow} alt="downArrow" style={{ rotate: `${subLink ? "180deg" : "0deg"}`, width: "13px", marginLeft: "5px" }} />
                </span>
              </div>
              {subLink && (
                <ul className="sidebar-menu sidebar-sub-menu">
                  {(hasCreateNewRolePermission || hasManageUserPermissionsPermission) && (
                    <li
                      onClick={() => {
                        navigate.push(ROUTES.ROLE_EMPLOYEES.ROLES_PERMISSIONS);
                        onClose();
                      }}
                    >
                      Roles and Permissions
                    </li>
                  )}
                  {(hasCreateNewDepartmentPermission || hasAddEmployeePermission) && (
                    <li
                      onClick={() => {
                        navigate.push(ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT);
                        onClose();
                      }}
                    >
                      Employee Management
                    </li>
                  )}
                </ul>
              )}
            </li>
          )}
          {hasAnyHistoryPermission && (
            <li className="sub-menu-bar" onClick={() => setHistory(!history)}>
              <div className="sub-menu-list">
                <HistoryIcon />{" "}
                <span>
                  History{" "}
                  <Image src={downArrow} alt="downArrow" style={{ rotate: `${history ? "180deg" : "0deg"}`, width: "13px", marginLeft: "5px" }} />
                </span>
              </div>
              {history && (
                <ul className="sidebar-menu sidebar-sub-menu">
                  {hasViewAuditLogsPermission && (
                    <li
                      onClick={() => {
                        navigate.push(ROUTES.ACTIVITY_LOGS);
                        onClose();
                      }}
                    >
                      Activity logs
                    </li>
                  )}
                  {hasArchivePermission && (
                    <li
                      onClick={() => {
                        navigate.push(ROUTES.ARCHIVE.ARCHIVE_JOBS_CANDIDATES);
                        onClose();
                      }}
                    >
                      Archive Jobs/Candidates
                    </li>
                  )}
                </ul>
              )}
            </li>
          )}
          <li>
            <NotificationIcon />
            Notifications
          </li>
          <li
            onClick={() => {
              navigate.push(ROUTES.DASHBOARD);
              onClose();
            }}
          >
            <ProfileIcon /> {t("my_profile")}
          </li>
          <li onClick={onHandleLogout}>
            <LogoutIcon className="strokeSvg" />
            Logout
          </li>
        </div>
      </div>

      {isOpen && <div className="overlay" onClick={onClose}></div>}
    </>
  );
};

export default MobileSidebar;
