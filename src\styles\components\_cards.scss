@use "../abstracts" as *;

.common-tab {
  @extend %listSpacing;
  display: flex;
  align-items: center;
  // border-radius: 14px;
  display: inline-flex;
  li {
    font-size: $text-md;
    font-weight: $medium;
    color: $dark;
    cursor: pointer;
    padding: 8px 30px;
    position: relative;
    margin: 0;
    text-align: center;
    min-width: 220px;
    border: 1px solid $dark;
    &:first-child {
      border-radius: 10px 0 0 10px;
    }
    &:last-child {
      border-radius: 0 10px 10px 0;
    }
    &.active {
      position: relative;
      color: $white;
      background: $primary;
      border-color: $primary;
    }
  }
}

.announcement-card {
  border-radius: 24px;
  background: rgba($primary, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  transition:
    transform 0.35s ease,
    box-shadow 0.35s ease;

  &:hover {
    transform: translateY(-4px); // move up
    box-shadow: 0 8px 20px rgba($primary, 0.25);
  }

  &:active {
    transform: translateY(4px); // press-down effect
    box-shadow: 0 3px 10px rgba($primary, 0.15);
  }

  .announcement-content {
    padding: 20px;
    transition: color 0.3s ease;

    h3 {
      color: $dark;
      font-size: $text-xl;
      font-weight: $bold;
      transition: color 0.3s ease;

      .announcement-card:hover & {
        color: $primary;
      }
    }

    @media (max-width: 991px) {
      padding: 15px;
      h3 {
        font-size: $text-md;
      }
    }
  }

  .announcement-image {
    padding: 10px 20px 0;

    .announce-img {
      width: 149px;
      height: 110px;
      object-fit: contain;
      object-position: bottom;
      transition: transform 0.35s ease;
    }

    .announcement-card:hover & .announce-img {
      transform: translateY(-5px); // image also lifts a bit
    }
  }
}

.hiring-card {
  // border-radius: 24px;
  background: $white;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 25px 30px 0 30px;
  border-radius: 32px;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.16);
  overflow: hidden;
  .hiring-content {
    width: 50%;
    padding-bottom: 25px;
    height: 150px;
    min-height: 150px;
    h3 {
      color: $dark;
      font-size: $text-lg;
      font-weight: $bold;
    }
    p {
      font-size: 1.4rem;
      color: $dark;
      font-style: normal;
      font-weight: $medium;
      &.space-bottom30 {
        margin-bottom: 30px;
      }
    }
    a {
      display: flex;
      align-items: center;
      gap: 6px;
      color: $primary;
      font-size: $text-sm;
      font-weight: $semiBold;
      margin-top: 10px;
      min-height: 20px;
      line-height: 20px;
      svg {
        width: 20px;
        height: 20px;
        fill: $primary;
      }
    }
  }
  .hiring-image {
    width: 50%;
    .hiring-img {
      width: 100%;
      height: 150px;
      min-height: 150px;
      object-fit: contain;
      object-position: bottom;
    }
  }
  &.active,
  &:hover {
    background: rgba($primary, 0.1);
  }
  @media (width <= 767px) {
    flex-direction: column;
    padding: 30px 20px 0;
    border-radius: 24px;
    .hiring-content {
      width: 100%;
    }
    .hiring-image {
      width: 100%;
    }
  }
}

.career-skill-card {
  border-radius: 24px;
  background: rgba($primary, 0.1);
  padding: 20px;
  height: 100%;
  .head {
    display: flex;
    justify-content: space-between;
    // align-items: center;
    align-items: flex-start;
    margin-bottom: 15px;
    h3 {
      color: $dark;
      font-size: $text-lg;
      font-weight: $bold;
    }
    .right-img {
      width: 20px;
      min-width: 20px;
      height: 20px;
      fill: $dark;
      cursor: pointer;
      path {
        fill: $dark;
      }
      &:active {
        opacity: 0.7;
      }
    }
    .skill-content {
      p {
        font-size: $text-md;
        color: $dark;
        font-weight: $medium;
      }
    }
  }
  &:hover,
  &.active {
    background: $secondary;
    transition: all 0.4s ease;
    .head {
      h3 {
        color: $white;
      }
      .right-img {
        path {
          fill: $white;
        }
      }
    }
    .skill-content {
      p {
        color: $white;
      }
    }
  }
}
.drag-skill-card {
  border-radius: 24px;
  background: rgba($secondary, 0.1);
  padding: 20px;
  cursor: move;
  transition: all 0.4s ease;
  &.primary-bg {
    background: linear-gradient(54deg, rgba(116, 168, 255, 0.3) 20.92%, rgba(170, 202, 255, 0.3) 52.91%, rgba(93, 134, 204, 0.3) 88.37%);
  }
  .head {
    margin-bottom: 7px;
    h3 {
      color: $dark;
      font-size: 1.6rem;
      font-weight: $bold;
    }
    .skill-content {
      p {
        font-size: 1.4rem;
        color: $dark;
        font-weight: $medium;
        line-height: 1.4;
      }
    }
  }
}
.assignments-card {
  .assignments-name {
    border-radius: 24px;
    background: rgba($secondary, 0.1);
    padding: 20px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    flex-direction: column;
    gap: 15px;
    h4 {
      color: $dark;
      font-size: $text-lg;
      font-weight: $medium;
    }
  }
  .assignments-list {
    @extend %listSpacing;
    margin-top: 20px;
    li {
      padding: 10px;
      border-radius: 8px;
      text-align: center;
      background: rgba($primary, 0.1);
      cursor: pointer;
      transition: all 0.4s ease;
      margin-bottom: 20px;
      font-size: $text-md;
      font-weight: $medium;
      &:active {
        opacity: 0.7;
      }
      &.selecting {
        background: $secondary;
        color: $dark;
      }
      &.selected {
        background: $primary;
        color: $white;
      }
    }
  }
}

.candidate-card {
  border-radius: 24px;
  background: $white;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.09);
  padding: 25px 25px;
  height: 100%;
  transition: all 0.3s ease-in-out; // smooth effect

  @media (max-width: 991px) {
    padding: 25px 15px;
  }

  &:hover {
    transform: translateY(-8px) scale(1.01); // smooth lift effect
    box-shadow: 0px 10px 25px rgba(0, 0, 0, 0.12);
  }

  h2 {
    font-size: 2rem;
    font-weight: $bold;
    color: $dark;
    margin-top: 0;
    margin-bottom: 15px;
    text-transform: capitalize;
    transition: color 0.3s ease;

    &.active-link {
      cursor: pointer;
      text-decoration: underline;
    }

    &:hover {
      color: $primary; // highlight heading on hover
    }
  }

  .title {
    font-size: 1.5rem;
    color: rgba($dark, 0.6);
    font-weight: $medium;
    margin-bottom: 5px;

    &:last-child {
      margin-bottom: 0;
    }

    @media (max-width: 767px) {
      font-size: $text-sm;
    }
  }

  .actions {
    margin-top: 15px;

    a {
      display: flex;
      align-items: center;
      text-decoration: none;
      font-size: 14px;
      color: $primary;
      margin-bottom: 10px;
      gap: 5px;
      transition: all 0.3s ease;

      &:hover {
        color: darken($primary, 10%);
        transform: translateX(4px); // nice link slide effect
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

//common card style ------------
.common-card {
  border-radius: 40px;
  background: $white;
  box-shadow: 0px 4px 12px 0px rgba($dark, 0.16);
  padding: 25px;
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid $dark;
    padding-bottom: 20px;
    h3 {
      color: $dark;
      font-size: $text-lg;
      font-weight: $bold;
    }
  }
  @media (max-width: 767px) {
    padding: 20px;
  }
}
//qualification-card
.qualification-card {
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 20px;
  // Skeleton styles
  &.skeleton-card {
    background-color: rgba($grey, 0.05);
    position: relative;
    overflow: hidden;

    &::after {
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      transform: translateX(-100%);
      background-image: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0,
        rgba(255, 255, 255, 0.2) 20%,
        rgba(255, 255, 255, 0.5) 60%,
        rgba(255, 255, 255, 0)
      );
      animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
      100% {
        transform: translateX(100%);
      }
    }

    .skeleton-text {
      height: 14px;
      background-color: rgba($dark, 0.1);
      border-radius: 4px;
      margin-bottom: 8px;
      width: 100%;

      &.skeleton-title {
        height: 24px;
        width: 70%;
        margin-bottom: 6px;
      }

      &.skeleton-subtitle {
        height: 16px;
        width: 50%;
      }

      &.skeleton-label {
        height: 18px;
        width: 40%;
        margin-bottom: 12px;
      }
    }

    .skeleton-circle {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: rgba($dark, 0.1);

      &.skeleton-icon {
        width: 50px;
        height: 50px;
        margin: 0 auto 15px;
      }
    }

    .skeleton-button {
      height: 40px;
      border-radius: 12px;
      background-color: rgba($dark, 0.1);
      width: 48%;
    }

    // Styles for skeleton skills grid
    .assignments-list {
      .skeleton-skill {
        height: 40px;
        border-radius: 8px;
        background-color: rgba($dark, 0.08);
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;

        .skeleton-text {
          width: 70%;
          height: 16px;
          margin-bottom: 0;
        }

        // Different background colors for selected skills
        &.skeleton-selected {
          &:nth-child(odd) {
            background-color: rgba($primary, 0.2);
          }

          &:nth-child(even) {
            background-color: rgba($secondary, 0.2);
          }

          &:nth-child(3n) {
            background-color: rgba(#3b82f6, 0.2);
          }

          &:nth-child(4n) {
            background-color: rgba(#6366f1, 0.2);
          }
        }
      }
    }
  }
  &.rejected-card {
    background-color: rgba(#d00000, 0.05);
  }
  &.approved-card {
    background-color: rgba(#007733, 0.05);
  }
  .qualification-card-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    .name {
      h3 {
        color: $dark;
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 4px;
      }
      p {
        font-size: 14px;
        color: $dark;
        font-weight: 400;
        margin: 0px;
      }
    }
    .top-right {
      .hold-icon {
        width: 30px;
        height: 30px;
        min-width: 30px;
        fill: $secondary;
      }

      .approved-status {
        cursor: pointer;
        p {
          font-size: 14px;
          color: $dark;
          font-weight: 600;
          margin: 0px;
          display: flex;
          align-items: center;
          gap: 6px;
          svg {
            width: 20px;
            height: 20px;
            min-width: 20px;
            &.down-arrow-icon {
              width: 15px;
              height: 15px;
              min-width: 15px;
            }
          }
        }
      }
    }
  }
  .qualification-card-mid {
    p {
      font-size: 14px;
      color: $dark;
      font-weight: 400;
      margin-bottom: 8px;
    }
  }
  .qualification-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
    button {
      min-width: 90px;
      text-transform: capitalize;
      @media (max-width: 767px) {
        width: 100%;
      }
      &.reject {
        background: transparent;
        color: #d00000;
        border: 1px solid #d00000;
      }
      &.approve {
        background: #007733;
        color: $white;
        border: 1px solid #007733;
      }
    }
  }
  .button-align {
    padding-top: 10px;
    .theme-btn {
      padding: 12px 20px;
    }
  }
}

//jobs-card
.jobs-card {
  border-radius: 24px;
  background-color: $white;
  padding: 20px;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.09);
  min-height: 115px;
  overflow: hidden;
  // box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.09);
  // cursor: url("/assets/images/cursor-click.svg"), auto !important;
  .name {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    h4 {
      color: $dark;
      font-size: 20px;
      font-weight: 700;
      word-break: break-word;
    }
    svg {
      width: 18px;
      height: 18px;
    }
  }
  .description {
    font-size: $text-sm;
    color: rgba($dark, 0.8);
    font-weight: $medium;
    margin-top: 8px;
  }
  &.active,
  &:hover {
    background-color: rgba($primary, 1);
    transition: all 0.4s ease;
    // cursor: url("/assets/images/cursor-click.svg"), auto !important;
    h4,
    .description {
      color: $white;
    }
    svg {
      path {
        fill: $white;
      }
    }
  }
  &:active {
    opacity: 0.7;
    transition: all 0.4s ease;
  }
}

//Conduct Interview role card style
.role-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 0;
  margin: 0;
  list-style: none;

  .role-item {
    background: rgba($primary, 0.1); // light gray-blue background
    border-radius: 12px;
    padding: 12px 30px;
    font-weight: $semiBold;
    color: $dark;
    font-size: 1.5rem;
    cursor: pointer;
    transition:
      background-color 0.3s ease,
      color 0.3s ease;
    user-select: none;

    &:hover,
    &:focus,
    &.active {
      background: rgba($primary, 1);
      outline: none;
      color: $white;
    }
  }
  &.secondary {
    .role-item {
      background: $white;
      color: $dark;
      border-radius: 16px;
      border: 1px solid rgba($dark, 0.15);
      box-shadow: 0px 7px 7px 0px rgba(0, 0, 0, 0.03);
      padding: 20px;
    }
    .role-item:hover,
    .role-item:focus,
    .role-item.active {
      background: rgba($secondary, 1);
      outline: none;
      color: $white;
    }
    //mobile
    @media screen and (max-width: 767px) {
      justify-content: flex-start;
      .role-item {
        border-radius: 10px;
        padding: 5px 15px;
        font-size: 14px;
      }
    }
  }
}
// Responsive typography and layout
@media (max-width: 767px) {
  .role-list {
    justify-content: center;
  }
  .role-item {
    font-size: $text-xs;
    padding: 8px 14px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .role-list {
    justify-content: flex-start;
  }
  .role-list__item {
    font-size: 0.95rem;
  }
}

@media (min-width: 1024px) {
  .role-list {
    justify-content: flex-start;
  }
  .role-item {
    font-size: $text-sm;
  }
}

// overview skill card style
.overview-skill-card {
  position: relative;
  background-color: rgba($secondary, 0.1);
  border-radius: 24px;
  padding: 30px;
  z-index: 0;
  overflow: hidden;
  height: 100%;
  padding-bottom: 45px;

  // smooth transition for everything
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  .overview-txt {
    font-size: $text-md;
    color: $dark;
    font-weight: $semiBold;
    margin-bottom: 0;
    transition: color 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    word-break: break-word;
  }

  .clear-btn {
    gap: 6px;
    color: $secondary;
    transition: color 0.6s cubic-bezier(0.4, 0, 0.2, 1);

    svg {
      width: 15px;
      height: 15px;
      fill: $secondary;
      transition: fill 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  &::after {
    content: attr(data-text);
    position: absolute;
    z-index: -1;
    top: 7px;
    right: 11px;
    font-size: 15px;
    font-weight: $bold;
    color: #fff;
  }

  &::before {
    content: "";
    position: absolute;
    z-index: -1;
    top: -20px;
    right: -15px;
    background: #cb9932;
    height: 60px;
    width: 60px;
    border-radius: 100%;
    transform: scale(1);
    transform-origin: 50% 50%;
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  // hover state
  &:hover {
    .clear-btn {
      color: $white;
      svg {
        fill: $white;
      }
    }
    .overview-txt {
      color: $white;
    }
  }

  &:hover::before {
    transform: scale(100);
  }

  // mobile
  @media screen and (max-width: 767px) {
    padding: 15px;
    padding-bottom: 40px;

    .overview-txt {
      font-size: $text-sm;
      padding-right: 20px;
    }

    &::before {
      top: -28px;
      right: -21px;
    }

    &::after {
      top: 7px;
      right: 11px;
      font-size: 12px;
    }
  }
}

.interview-info {
  border-radius: 24px;
  background-color: rgba($secondary, 0.1);
  padding: 30px;
  margin-bottom: 20px;
  .info-item {
    margin-bottom: 15px;
    .info-title {
      font-size: 1.6rem;
      color: $dark;
      font-weight: $semiBold;
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      .dot {
        width: 8px;
        min-width: 8px;
        height: 8px;
        background-color: $dark;
        border-radius: 50%;
        display: inline-block;
        margin-right: 10px;
      }
    }
    p {
      font-size: $text-md;
      color: $dark;
      font-weight: $regular;
    }
    @media screen and (max-width: 576px) {
      width: 100% !important;
    }
  }

  //mobile
  @media screen and (max-width: 767px) {
    padding: 20px;
    .info-title {
      font-size: $text-sm;
      align-items: flex-start !important;
      .dot {
        margin-right: 10px;
        margin-top: 7px;
      }
    }
    p {
      font-size: $text-sm;
    }
  }
}
// interview question card style
.interview-question-card {
  border-radius: 16px;
  background-color: rgba($secondary, 0.1);
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  &.with-border {
    border: solid 2px rgba($secondary, 0.6);
    background: rgba(203, 153, 50, 0.05);
  }
  .tittle {
    font-size: 1.6rem;
    color: $secondary;
    font-weight: $bold;
    margin-bottom: 10px;
    svg {
      stroke: $secondary !important;
      margin-left: 6px;
      &.rotate {
        transform: rotate(180deg);
      }
    }
  }
  h5 {
    font-size: 1.6rem;
    color: $dark;
    font-weight: $semiBold;
    line-height: 1.4;
    word-break: break-word;
  }
  .question-body {
    margin-top: 15px;
    .form-group {
      margin-bottom: 0;
      .custom-textarea {
        overflow: hidden;
        border-radius: 16px;
      }
      textarea {
        background-color: $white;
        border-radius: 16px;
        border: 1px solid rgba($dark, 0.4);
        padding: 10px;
      }
    }
    .follow-up-container {
      display: flex;
      align-items: center;
      margin-top: 15px;
      gap: 30px;
      .follow-up-btn {
        display: flex;
        gap: 5px;
        align-items: center;
      }
      .follow-up-text {
        font-size: $text-md;
        color: $dark;
        font-weight: $medium;
        span {
          font-weight: $bold;
        }
      }
    }
    .answer-strap {
      // margin-top: 15px;
      padding: 14px 12px;
      border-radius: 8px;
      border: 1px solid transparent;
      width: 100%;
      margin: 5px 0;

      .radio-wrapper {
        gap: 10px;
        margin-bottom: 0;
        line-height: 16px;
        align-items: center;
        .radio-input {
          width: 24px;
          height: 24px;
          min-width: 24px;
          min-height: 24px;
          --bs-form-check-bg-image: url("../../../public/assets/images/check-bg-image-white.svg");
          // &:checked {
          //   background-color: rgba($dark, 0.8);
          // }
        }
        .radio-label {
          font-size: 1.6rem;
          color: $dark;
          font-weight: $medium;
        }
        // .form-check-input:checked[type="radio"] {
        //   --bs-form-check-bg-image: url("../../../public/assets/images/check-bg-image-white.svg") !important;
        // }
      }
      &.right-answer {
        color: $dark;
        border: 1px solid rgba($green, 0.6);
        background-color: rgba($green, 0.08);
        .radio-wrapper {
          .radio-label {
            color: $dark;
          }
          .radio-input {
            border-color: rgba($green, 0.5);
            --bs-form-check-bg-image: url("../../../public/assets/images/check-bg-image-green.svg");
          }
        }
      }
      &.candidate-answer {
        color: $dark;
        .radio-wrapper {
          .radio-label {
            color: $dark;
          }
          .radio-input {
            border-color: $secondary;
            --bs-form-check-bg-image: url("../../../public/assets/images/check-bg-image-primary.svg");
          }
        }
      }
    }
    .note-text {
      background: rgba($dark, 0.2);
      padding: 6px 12px;
      border-radius: 8px;
      font-size: 1.5rem;
      color: #000;
      font-weight: $medium;
      margin-top: 15px;
    }
  }
  @media (max-width: 767px) {
    padding: 15px;
  }
}

// summary text card style
.summary-text-card {
  // border-radius: 24px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  // background: rgba($primary, 0.05);
  // border: solid 2px rgba($primary, 0.6);
  border-radius: 20px;
  border: 3px solid var(--Stratum-GPT-Gradient, #74a8ff);
  background: linear-gradient(54deg, rgba(116, 168, 255, 0.1) 20.92%, rgba(170, 202, 255, 0.1) 52.91%, rgba(93, 134, 204, 0.1) 88.37%);
  .tittle {
    font-size: $text-xl;
    color: $primary;
    font-weight: $bold;
    margin-bottom: 20px;
  }
  .sub-tittle {
    font-size: 2.2rem;
    color: $dark;
    font-weight: $bold;
    margin: 20px 0;
  }
  p {
    font-size: $text-md;
    color: $dark;
    font-weight: $regular;
  }
  ul {
    margin: 15px 0;
    padding-left: 25px;
    li {
      margin-bottom: 5px;
      &:last-child {
        margin-bottom: 0;
      }
      &::marker {
        color: $dark;
        font-weight: $bold;
        font-size: 14px;
      }
    }
    &.check-list {
      list-style: none;
      padding-left: 0;
      li {
        margin-bottom: 5px;
        font-size: $text-md;
        color: $dark;
        font-weight: $semiBold;
        display: flex;
        align-items: flex-start;
        gap: 5px;
        margin-bottom: 10px;
        &:last-child {
          margin-bottom: 0;
        }
        svg {
          min-width: 19px;
          min-height: 19px;
          margin-top: 3px;
        }
      }
    }
  }
  .big-text {
    font-size: 10rem;
    font-weight: $bold;
    color: $primary;
    line-height: 12rem;
    background: linear-gradient(54deg, #74a8ff 20.92%, #aacaff 52.91%, #5d86cc 88.37%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  // candidate card style in summary-text-card
  .candidate-profile {
    border: none;
    background: transparent;
    margin: 0;
    .info-container {
      justify-content: flex-start !important;
      .info-item {
        width: 22%;
        @media (max-width: 991px) {
          width: 100% !important;
        }
      }
    }
  }
  @media (max-width: 767px) {
    .big-text {
      font-size: 5rem;
      line-height: normal;
    }
    .sub-tittle {
      font-size: $text-md;
      margin: 10px 0;
    }
    p {
      font-size: $text-sm;
    }
    ul {
      margin: 5px 0;
    }
  }
}

// progress bar style
.progress-container {
  .time {
    font-weight: $bold;
    white-space: nowrap;
    font-size: $text-xl;
  }
  .progress-tracker {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 0px;
    margin-bottom: 60px;
    .bar-container {
      flex: 1;
      position: relative;
      .bar {
        position: relative;
        height: 6px;
        background: #e0e0e0;
        border-radius: 2px;

        .progress {
          position: absolute;
          height: 100%;
          background: $secondary; // gold-like
          border-radius: 2px;
          transition: width 0.3s ease;
          overflow: visible;
          &::after {
            content: "";
            position: absolute;
            top: -2px;
            right: -7px;
            width: 10px;
            height: 10px;
            background: $secondary;
            box-shadow: rgba($secondary, 0.3) 0px 0px 0px 3px;
            border-radius: 50%;
            z-index: 999;
          }
        }

        .marker {
          position: absolute;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 1px;
          height: 12px;
          background: #e0e0e0;
          border: 1.5px solid $secondary;

          &.active {
            background: $secondary;
          }
        }
      }

      .labels {
        position: relative;
        display: flex;
        justify-content: space-between;
        font-size: $text-sm;
        color: $dark;

        .label {
          position: absolute;
          // transform: translateX(-50%);
          white-space: nowrap;
          top: 100%;
          font-size: $text-sm;
          margin-top: 20px;
          font-weight: $semiBold;
          @media (max-width: 375px) {
            font-size: 0.75rem !important;
          }
        }
      }
    }
  }
  .status {
    background-color: #ffeaea;
    color: #ff4a5c;
    font-size: $text-xs;
    padding: 0px 10px;
    border-radius: 20px;
    font-weight: $bold;
    white-space: nowrap;
    min-height: 30px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    text-transform: capitalize;
    border: 1px solid #ff4a5c;
    .status-lottie {
      width: 50px;
      height: 28px;
    }
    .record-icon {
      width: 11px;
      height: 11px;
      border-radius: 100%;
      background: #ff4a5c;
      display: inline-block;
      margin-right: 5px;
    }
  }
  @media (max-width: 991px) {
    .time {
      font-size: $text-sm;
      margin-bottom: 0;
    }
    .status {
      font-size: 0.9rem;
      min-height: 24px;
      margin-bottom: 0;
    }
    .progress-tracker {
      .bar-container {
        .labels {
          .label {
            font-size: 0.9rem;
          }
        }
      }
    }
  }
}

.behavioral-letter-card {
  padding: 20px;
  border-radius: 0;
  background-color: rgba($primary, 0.1);
  height: 100%;
  position: relative;
  padding-bottom: 76px;
  max-height: 550px;
  min-height: 550px;
  &::after {
    content: "";
    position: absolute;
    background: url("../../../public/assets/images/letter-pin.png");
    background-size: contain;
    background-repeat: no-repeat;
    top: -10px;
    right: 5px;
    z-index: 2;
    width: 50px;
    height: 65px;
  }
  .form-group {
    textarea {
      margin-top: 10px;
      height: 100%;
      background: transparent;
      padding: 0;
      border: none;
      border-radius: 0;
      padding-right: 6px;
    }
  }
  h5 {
    font-size: $heading-xs;
    color: $dark;
    font-weight: $bold;
    margin-bottom: 10px;
    span {
      color: $primary;
    }
  }
  p {
    font-size: $text-md;
    color: rgba($dark, 0.5);
    font-weight: $medium;
    &.font-dark {
      color: $dark;
    }
  }
  .fold-svg {
    position: absolute;
    bottom: 0;
    left: 0;
    background: $white;
    height: 66px;
  }
  .update-btn {
    position: absolute;
    bottom: 20px;
    right: 20px;
  }
  @media (max-width: 991px) {
    max-height: max-content;
    min-height: auto;
    margin-bottom: 30px;
  }
}

.interview-topic-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  @extend %listSpacing;
  margin-bottom: 30px;

  .topic-item {
    background: rgba($white, 1);
    border-radius: 14px;
    padding: 12px 24px;
    font-weight: $medium;
    color: $dark;
    font-size: 1.4rem;
    cursor: pointer;
    border: 1px solid rgba($dark, 0.15);
    box-shadow: 0px 7px 7px 0px rgba(0, 0, 0, 0.03);
    position: relative;

    &.current {
      background: $secondary;
      color: $white;
      .interviewer-name {
        background: $secondary;
        color: $white;
      }
    }
    &.completed {
      background: $primary;
      color: $white;
    }
    &.additional {
      background: rgba($green, 1);
      color: $white;
    }
    .interviewer-name {
      background: rgba($secondary, 1);
      color: $white;
      height: 30px;
      width: 30px;
      min-height: 30px;
      min-width: 30px;
      border-radius: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2rem;
      text-transform: uppercase;
      border: 2px solid $white;
      position: absolute;
      top: -10px;
      right: -10px;
    }
  }
  @media (max-width: 991px) {
    gap: 10px;
    .topic-item {
      padding: 10px 15px;
      font-weight: 600;
      color: #333;
      font-size: 1.4rem;
    }
  }
}

.number-task {
  display: flex;
  gap: 15px;
  @extend %listSpacing;
  margin: 20px 0;
  li {
    font-size: $text-md;
    color: $dark;
    font-weight: $bold;
    padding: 7px 15px;
    border-radius: 8px;
    background-color: rgba($white, 1);
    cursor: pointer;
    border: 1px solid rgba($dark, 1);
    transition: all 0.3s ease;
    &:hover,
    &.active {
      background-color: rgba($secondary, 1) !important;
      color: $dark;
      border-color: rgba($secondary, 1);
    }
    &.extreme {
      &:hover,
      &.active {
        background-color: rgba($danger, 1) !important;
        color: $white;
        border-color: rgba($danger, 1);
      }
    }
  }
  @media (max-width: 991px) {
    gap: 15px;
    flex-wrap: wrap;
    li {
      font-size: 1.2rem;
      padding: 7px 14px;
    }
  }
}

.user-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba($secondary, 1);
  padding: 20px;
  border-radius: 14px;
  margin-bottom: 20px;

  .info {
    display: flex;
    flex-direction: column;
    color: #fff;

    .user-name {
      font-size: $text-md;
      font-weight: $bold;
    }

    .user-role {
      font-size: $text-sm;
      font-weight: $regular;
      margin-top: 8px;
    }
  }

  .actions {
    display: flex;
    gap: 20px;
    align-items: center;
    position: relative;
    &::after {
      content: "";
      width: 1px;
      height: 100%;
      background-color: rgba($white, 0.2);
      position: absolute;
      right: 10px;
      left: 0px;
      top: 0;
      margin: auto;
    }
    button {
      gap: 5px;
      text-decoration: underline;
      svg {
        fill: $white;
      }
    }
  }
}

.interview-summary {
  background-color: rgba($secondary, 0.1);
  border-radius: 14px;
  padding: 20px;
  overflow: hidden;
  margin-bottom: 20px;
  .interview-summary-inner-card {
    padding-top: 30px;
    margin-top: 25px;
    position: relative;
    &::before {
      content: "";
      width: 100vw;
      height: 1px;
      background-color: $secondary;
      position: absolute;
      left: -20px;
      right: 0;
      top: 0;
      margin: auto;
    }
    &.saprator-none {
      margin: 0;
      padding: 0;
      &::before {
        display: none;
      }
    }
  }

  .summary-header {
    background-color: rgba($secondary, 1);
    padding: 15px 20px;
    margin: -20px -20px 20px;
    .summary-heading {
      font-size: 1.8rem;
      font-weight: $medium;
      color: $white;
      margin: 0;
    }
  }

  // interviewer style
  .interviewer {
    margin-bottom: 25px;
    .interviewer-info {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    .interviewer-avatar {
      width: 30px;
      height: 30px;
      border-radius: 50%;
    }
    .interviewer-name {
      font-size: $text-md;
      font-weight: $medium;
      color: $dark;
    }
    .large {
      .interviewer-avatar {
        width: 45px;
        height: 45px;
      }
      .interviewer-name {
        font-size: $text-xl;
        font-weight: $bold;
      }
    }
  }
  .summary-title {
    font-size: $text-lg;
    font-weight: $bold;
    color: $dark;
    margin-bottom: 15px;
  }
  // scores style
  .summary-scores {
    .score-btns {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }
  }
  // summary-highlights styles
  .summary-highlights {
    margin-top: 20px;
    .highlight-list {
      margin-bottom: 0;
      padding-left: 25px;
      .highlight-item {
        position: relative;
        font-size: $text-md;
        color: $dark;
        margin-bottom: 10px;
        word-break: break-word;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  // background-heading styles
  .background-heading {
    border-radius: 10px;
    background: rgba($secondary, 0.1);
    padding: 15px;
    margin-bottom: 20px;
    h4 {
      font-size: 1.8rem;
      font-weight: $bold;
      color: $dark;
      line-height: 1.2;
      margin-bottom: 10px;
    }
    p {
      font-size: $text-md;
      color: rgba($dark, 1);
      font-weight: $semiBold;
      margin-bottom: 0;
    }
  }
  .section-heading {
    h2 {
      font-size: 1.8rem;
      margin-bottom: 10px;
    }
    p {
      font-size: 1.4rem;
    }
    .operation-text {
      span {
        color: $dark;
        font-size: $text-md;
        margin: 0;
        display: block;
        &:not(:last-child) {
          color: rgba($dark, 0.7);
          &::after {
            content: "|";
            margin: 0 2px;
          }
        }
      }
    }
  }
  .number-task {
    padding-left: 0;
    margin: 15px 0;
    gap: 8px;
    display: inline-flex;
    li {
      font-size: 1.4rem;
      padding: 2px 10px;
      border-radius: 6px;
      background: transparent;
    }
  }
  .number-task-text {
    font-size: $text-sm;
    background: rgba($secondary, 0.1);
    font-weight: $medium;
    margin-bottom: 0;
    padding: 5px 10px;
    color: $dark;
    border-radius: 10px;
    width: max-content;
  }
  .form-control {
    background: $white !important;
    color: $dark !important;
    &::placeholder {
      color: $dark !important;
    }
  }
}

.profile-section {
  .candidate-profile {
    display: flex;
    align-items: center;
    gap: 25px;
    padding-bottom: 30px;
    margin-bottom: 30px;
    border-bottom: 1px solid rgba($dark, 0.2);
    &.user-profile {
      padding: 20px;
      position: relative;
      z-index: 1;
      gap: 20px;
      margin-top: 25px;
      padding-bottom: 35px;
      // border-radius: 12px;
      &::after {
        content: "";
        position: absolute;
        top: -28px;
        left: 0;
        width: 100%;
        height: 100px;
        border-radius: 12px;
        background: linear-gradient(0deg, #6b90ca 6.6%, #436eb6 91.1%);
        z-index: -1;
        @media screen and (max-width: 991px) {
          height: 100px;
        }
      }
      .candidate-name {
        color: $white;
        font-size: $text-xl;
        text-transform: capitalize;
      }
      .candidate-role {
        font-size: $text-lg;
        font-weight: $semiBold;
        color: $dark;
        margin-top: 20px;
      }
    }
  }
  .candidate-name {
    font-size: 2.4rem;
    font-weight: $bold;
    color: $dark;
  }
  .candidate-info {
    width: 100%;
    .info-container {
      display: flex;
      gap: 15px;
      margin-top: 15px;
      align-items: flex-start;
      justify-content: space-between;
      .info-item {
        .info-title {
          font-size: $text-xs;
          font-weight: $medium;
          color: $dark;
        }
        .info-value {
          font-size: $text-sm;
          color: rgba($dark, 1);
          font-weight: $semiBold;
          &.with-img {
            display: flex;
            align-items: center;
            gap: 5px;
            img {
              width: 18px;
              min-width: 18px;
              height: 18px;
              min-height: 18px;
              border-radius: 100%;
              object-fit: cover;
            }
          }
        }
        @media screen and (max-width: 576px) {
          width: 100% !important;
          padding-left: 15px;
        }
      }
    }
    &.user-info-md {
      .info-container {
        .info-item {
          @media screen and (min-width: 1200px) {
            max-width: 20%;
          }
          .info-title {
            font-size: 1.4rem;
          }
          .info-value {
            font-size: 1.4rem;
            white-space: pre-line !important;
            word-break: break-all !important;
            @media (max-width: 991px) {
              white-space: pre-line !important;
              word-break: break-all !important;
            }
          }
        }
      }
    }
    .final-decision-icon {
      width: 90px;
      height: 90px;
      margin-top: -50px;
      margin-right: 40px;
    }
  }
  .candidate-image {
    width: 100px;
    min-width: 100px;
    height: 100px;
    min-height: 100px;
    overflow: hidden;
    object-fit: cover;
    border-radius: 12px;
  }
  @media (max-width: 991px) {
    flex-direction: column;
    align-items: flex-start;
    .candidate-info {
      .candidate-name {
        font-size: 2rem;
      }
      .info-container {
        flex-wrap: wrap;
        gap: 10px;

        .info-item {
          width: calc(50% - 5px);
          p {
            white-space: nowrap;
          }
        }
        .theme-btn {
          padding: 1rem 1.5rem;
          margin-top: 10px;
        }
      }
    }

    .candidate-image {
      width: 80px;
      min-width: 80px;
      height: 80px;
      min-height: 80px;
    }
  }
  .circular-progress-card {
    padding: 20px;
    height: 200px;
    overflow: hidden;
    .circular-progress-bar {
      width: 100%;
      height: 345px;
      object-fit: contain;
      margin-top: -15px;
      font-weight: $semiBold;
    }
  }
  .skills-graph-card {
    canvas {
      height: 305px !important;
      width: 100% !important;
      height: 100% !important;
      object-fit: contain;
    }
  }
}

.plan-info-card {
  background-color: #e7f0ff;
  border-radius: 16px;
  padding: 20px;
  .plan-title {
    font-size: $text-xl;
    font-weight: $bold;
    color: $dark;
  }
  .plan-status-container {
    display: flex;
    align-items: center;
    gap: 20px;
    padding-top: 8px;
    .plan-status {
      font-size: $text-md;
      font-weight: $semiBold;
      color: $green;
      &.inactive {
        color: $danger;
      }
    }
    .plan-price {
      font-size: $text-md;
      font-weight: $semiBold;
      color: $dark;
      position: relative;
      margin-bottom: 0;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        left: -10px;
        width: 2px;
        height: 70%;
        background-color: rgba($dark, 0.2);
        margin: auto 0;
      }
      .plan-access-text {
        font-size: 0.9rem;
        font-weight: $medium;
        padding: 1px 6px;
        background-color: rgba($green, 1);
        border-radius: 6px;
        color: $white;
        display: inline-block;
        margin-left: 10px;
      }
    }
  }
  .plan-billing {
    font-size: $text-sm;
    font-weight: $medium;
    color: $dark;
    margin-top: 5px;
  }
  .plan-actions {
    display: flex;
    gap: 10px;
  }
}
.script-generate-card {
  background-color: $white;
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba($dark, 0.2);
  .script-header {
    display: flex;
    align-items: center;
    gap: 3%;
    .script-title {
      font-size: $text-xl;
      font-weight: $bold;
      color: $dark;
    }
    .script-generate-text {
      font-size: $text-lg;
      font-weight: $medium;
      color: $primary;
      cursor: pointer;
      text-decoration: underline;
    }
  }

  .script-generator {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 15px;
    .script-card {
      font-size: $text-sm;
      font-weight: $medium;
      color: $dark;
      padding: 10px;
      border-radius: 12px;
      background: rgba(51, 51, 51, 0.05);
      transition: all 0.3s ease;
      width: 100%;
      cursor: auto;
      font-size: 1.4rem;
      font-weight: $medium;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .copy-icon {
      width: 25px;
      min-width: 25px;
      height: 25px;
      cursor: pointer;
      transition: all 0.4s ease;
      &:active {
        transform: scale(0.9);
        opacity: 0.7;
        transition: all 0.4s ease;
      }
    }
  }
}

// notifications pop up
.notifications {
  max-width: 440px;
  min-width: 440px;
  position: absolute;
  top: 100px;
  right: 15%;
  z-index: 1000;
  background-color: $white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.16);
  @media (max-width: 991px) {
    max-width: 100%;
    min-width: 100%;
    right: 0;
    top: 70px;
  }
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    h3 {
      font-size: $text-xl;
      font-weight: $bold;
      color: $dark;
    }
    .clear-btn {
      font-size: $text-md;
      font-weight: $medium;
      color: $primary;
    }
  }
  .read-btns {
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 15px;
    button {
      font-size: $text-md;
      font-weight: $medium;
      padding: 6px 10px;
      border-radius: 6px;
      min-width: 60px;
      border: none;
      &.grey-btn {
        color: rgba($dark, 0.5);
        background: rgba($dark, 0.1);
      }
    }
  }
  .notification-wrapper {
    height: 400px;
    overflow-y: auto;
    padding-right: 5px;
    .notification-item {
      padding: 0;
      margin-bottom: 10px;
      border-bottom: 1px solid rgba($dark, 0.2);
      position: relative;
      cursor: pointer;
      padding-right: 20px;
      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
        margin-bottom: 0;
      }
      h4 {
        font-size: $text-md;
        font-weight: $bold;
        color: $dark;
        padding-bottom: 10px;
        span {
          color: $primary;
        }
      }
      p {
        font-size: $text-sm;
        color: $dark;
        padding-bottom: 10px;
        &.time {
          margin-bottom: 0;
        }
      }
      &.unread {
        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          top: 0;
          right: 15px;
          margin: auto 0;
          width: 6px;
          height: 6px;
          border-radius: 100%;
          background-color: $danger;
        }
      }
    }
  }
}

// .candidate profile page cards styles
.skills-score-card {
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  border-radius: 20px;
  border: 2px solid rgba(51, 51, 51, 0.2);
  background: #fff;
  .skills-list {
    list-style: none;
    padding: 0;
    margin: 0;
    .skills-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid rgba($dark, 0.2);
      margin-bottom: 0;
      &:last-child {
        border-bottom: none;
        padding-bottom: 0px;
      }
      &:first-child {
        padding-top: 0px;
      }
      .skill-name {
        font-size: $text-md;
        color: $dark;
        font-weight: $semiBold;
      }
      .skill-rating {
        font-size: $text-md;
        color: $dark;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: $medium;
      }
      .skill-badge {
        font-size: $text-md;
        color: $danger;
        font-weight: $medium;
      }
    }
  }
}

.skills-summary-card {
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  border-radius: 20px;
  border: 2px solid rgba(51, 51, 51, 0.2);
  background: #fff;
  min-height: 390px;

  .skills-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 15px;
    .skill-tag {
      font-size: 1.4rem;
      padding: 10px 15px;
      border-radius: 12px;
      background: rgba(51, 51, 51, 0.1);
      color: rgba($dark, 0.6);
      font-weight: $medium;
      &.active {
        background: rgba($primary, 1);
        color: $white;
      }
    }
  }
  .skill-sub-title {
    font-size: 1.8rem;
    font-weight: $bold;
    color: $dark;
    margin-bottom: 15px;
  }
  .strengths {
    .strength-item {
      font-size: 1.6rem;
      color: $dark;
      margin-bottom: 10px;
      line-height: 1.2;
      position: relative;
    }
  }
  .probability-card {
    border-left: 1px solid rgba($dark, 0.2);
    padding-left: 20px;

    .progress-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 30px;
      h3 {
        font-size: $heading-sm;
        font-weight: $bold;
        color: $secondary;
        margin: 0;
      }
      .probability-bar {
        display: flex;
        align-items: center;
        width: 100%;
        gap: 5px;
        .bar {
          width: 10%;
          height: 35px;
          background-color: #f0f0f0;
          border-radius: 5px;
          overflow: hidden;
          display: block;
          &.filled {
            background-color: $secondary;
          }
        }
      }
    }
  }
  .insight-card {
    background-color: rgba($secondary, 0.1);
    border-radius: 24px;
    padding: 30px;
    margin-top: 34px;
    .insight-title {
      font-size: $text-md;
      font-weight: $bold;
      color: $dark;
      margin-bottom: 10px;
      margin: 0;
    }
    .insight-text {
      font-size: $text-sm;
      color: rgba($dark, 0.8);
      font-weight: $medium;
      margin-top: 15px;
    }
  }
}

.improvement-areas-card {
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  border-radius: 20px;
  border: 2px solid rgba(51, 51, 51, 0.2);
  background: #fff;
  .improvement-card {
    padding: 25px;
    border-radius: 24px;
    background: linear-gradient(54deg, rgba(116, 168, 255, 0.3) 20.92%, rgba(170, 202, 255, 0.3) 52.91%, rgba(93, 134, 204, 0.3) 88.37%);
    .title {
      font-size: 1.9rem;
      font-weight: $bold;
      color: $dark;
      margin-bottom: 15px;
    }
    .description {
      font-size: 1.6rem;
      color: rgba($dark, 0.8);
      line-height: 1.4;
    }
  }
}

// job generate doc card style
// summary text card style
.job-generate-head {
  border-bottom: 2px solid rgba($dark, 0.2);
  padding-bottom: 15px;
  position: relative;
  &::after {
    content: "Or Enter Details Manually";
    position: absolute;
    bottom: -10px;
    left: 25px;
    width: max-content;
    background: #fff;
    font-size: 14px;
    display: inline-block;
    padding: 0 10px;
    font-weight: $medium;
    color: rgba($dark, 0.8);
    background: rgba($white, 1);
  }
  .job-generate-doc-card {
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    border-radius: 20px;
    border: 2px solid var(--Stratum-GPT-Gradient, #74a8ff);
    background: linear-gradient(53.6deg, rgba(116, 168, 255, 0.25) 20.92%, rgba(170, 202, 255, 0.25) 52.91%, rgba(93, 134, 204, 0.25) 88.37%);
    .upload-card {
      background: rgba($white, 0.7);
      min-height: 115px;
    }
    .tittle {
      font-size: $text-md;
      color: $primary;
      font-weight: $bold;
      margin-bottom: 20px;
    }
    .sub-tittle {
      font-size: 1.8rem;
      color: $dark;
      font-weight: $bold;
      margin: 20px 0;
    }
    p {
      font-size: 1.4rem;
      color: $dark;
      font-weight: $regular;
    }
    .upload-box-inner {
      svg {
        width: 32px !important;
        min-width: 32px !important;
        height: 32px !important;
        margin-bottom: 10px;
      }
      p {
        font-size: 1.2rem !important;
        line-height: 16px !important;
      }
    }
    .uploading-message {
      font-size: 1.2rem !important;
    }
  }
}
.feedback-result-card {
  padding: 25px;
  border-radius: 24px;
  background: linear-gradient(54deg, rgba(116, 168, 255, 0.3) 20.92%, rgba(170, 202, 255, 0.3) 52.91%, rgba(93, 134, 204, 0.3) 88.37%);
  .title {
    font-size: 2.2rem;
    font-weight: $bold;
    color: $dark;
    margin-bottom: 15px;
    span {
      color: $primary;
    }
  }
  .description {
    font-size: 1.6rem;
    color: rgba($dark, 0.8);
    line-height: 1.4;
    font-weight: $medium;
  }
  .status-icon {
    width: 100px;
    height: 100px;
  }
}
// Pending Feedbacks styles
.pending-feedbacks-section {
  background: rgba($secondary, 0.05);
  padding: 20px;
  border-radius: 16px;
  margin-bottom: 20px;
  margin-top: 20px;
  .row {
    flex-direction: row;
    flex-wrap: nowrap !important;
    overflow-x: auto;
    max-width: 100%;
    padding-bottom: 15px;
  }
  h3 {
    font-size: $text-md;
    font-weight: $bold;
    color: $danger;
    margin-bottom: 15px;
  }
  .pending-feedbacks__content {
    border-right: 1px solid rgba($dark, 0.2);
    padding-left: 10px;
    height: 100%;
    &.border-none {
      border-right: none;
    }

    &__position {
      font-size: $text-lg;
      font-weight: $bold;
      color: $dark;
      margin-top: 0;
      margin-bottom: 6px;
      text-transform: capitalize;
    }
    &__date {
      font-size: $text-sm;
      color: rgba($dark, 0.8);
      font-weight: $medium;
      margin: 0;
      margin-bottom: 1px;
    }
  }
}
