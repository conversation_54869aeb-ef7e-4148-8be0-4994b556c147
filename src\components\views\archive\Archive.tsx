import React, { useEffect, useState, useRef, useCallback } from "react";
import styles from "../../../styles/commonPage.module.scss";
import style from "@/styles/commonPage.module.scss";
import Button from "@/components/formElements/Button";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import ThreeDotsIcon from "@/components/svgComponents/ThreeDotsIcon";
import { updateJobStatus } from "@/services/jobRequirements/updateJobServices";
import { fetchJobsMeta, Job } from "@/services/jobRequirements/jobServices";
import { useTranslations } from "next-intl";
import { fetchCandidatesApplications } from "@/services/CandidatesServices/candidatesApplicationServices";
import { AuthState } from "@/redux/slices/authSlice";
import { useHasPermission } from "@/utils/permission";
import { useSelector } from "react-redux";
import { PERMISSION } from "@/constants/commonConstants";
import { archiveActiveApplication } from "@/services/CandidatesServices/candidatesApplicationStatusUpdateService";
import NoDataFoundIcon from "@/components/svgComponents/NoDataFoundIcon";
import { CandidateApplication } from "@/interfaces/candidatesInterface";
import InfiniteScroll from "react-infinite-scroll-component";
import RestoreIcon from "@/components/svgComponents/RestoreIcon";
import { DEFAULT_LIMIT } from "@/constants/commonConstants";
import TableSkeleton from "../skeletons/TableSkeleton";
import ConfirmationModal from "@/components/commonModals/ConfirmationModal";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { useTranslate } from "@/utils/translationUtils";

const ARCHIVE_TABS = {
  CANDIDATES: "Candidates",
  JOBS: "Jobs",
} as const;

type ArchiveTab = (typeof ARCHIVE_TABS)[keyof typeof ARCHIVE_TABS];

function Archive() {
  // Check if user has permissions for candidates and jobs using the simplified hook
  const hasArchiveRestoreJobPermission = useHasPermission(PERMISSION.ARCHIVE_RESTORE_JOB_POSTS);
  const hasArchiveRestoreCandidatesPermission = useHasPermission(PERMISSION.ARCHIVE_RESTORE_CANDIDATES);
  // Set default tab based on permissions
  const hasAnyPermission = hasArchiveRestoreCandidatesPermission || hasArchiveRestoreJobPermission;
  const defaultTab = hasArchiveRestoreCandidatesPermission
    ? ARCHIVE_TABS.CANDIDATES
    : hasArchiveRestoreJobPermission
      ? ARCHIVE_TABS.JOBS
      : ARCHIVE_TABS.CANDIDATES;
  const [selectedTab, setSelectedTab] = useState<ArchiveTab>(defaultTab);
  const [candidates, setCandidates] = useState<CandidateApplication[]>([]);
  const [candidateOffset, setCandidateOffset] = useState(0);
  const [hasMoreCandidates, setHasMoreCandidates] = useState(true);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [jobOffset, setJobOffset] = useState(0);
  const [hasMoreJobs, setHasMoreJobs] = useState(true);
  const [loadingCandidates, setLoadingCandidates] = useState(false);
  const [loadingJobs, setLoadingJobs] = useState(false);
  const [isTabSwitching, setIsTabSwitching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);
  const userData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const t = useTranslations();
  const translate = useTranslate();
  const isFetchingCandidates = useRef(false);
  const isFetchingJobs = useRef(false);
  const [confirmationModal, setConfirmationModal] = useState({
    isOpen: false,
    title: "",
    message: "",
    confirmButtonText: "",
    onConfirm: () => {},
    loading: false,
  });

  const closeConfirmationModal = () => {
    setConfirmationModal({
      isOpen: false,
      title: "",
      message: "",
      confirmButtonText: "",
      onConfirm: () => {},
      loading: false,
    });
  };

  const fetchMoreCandidatesApplications = useCallback(
    async (currentOffset = 0, reset = false) => {
      if (!userData?.orgId || isFetchingCandidates.current) return;
      console.log("Fetching candidates, loading state:", loadingCandidates);
      isFetchingCandidates.current = true;
      if (!loadingCandidates) setLoadingCandidates(true);

      try {
        const response = await fetchCandidatesApplications({
          page: currentOffset,
          limit: DEFAULT_LIMIT,
          isActive: false,
        });

        const newCandidates: CandidateApplication[] = response?.data?.data || [];
        if (Array.isArray(newCandidates) && newCandidates.length > 0) {
          setCandidates((prev) => (reset ? newCandidates : [...prev, ...newCandidates]));
          setHasMoreCandidates(newCandidates.length >= DEFAULT_LIMIT);
          setCandidateOffset(currentOffset + newCandidates.length);
        } else {
          setHasMoreCandidates(false);
        }
      } catch (error) {
        console.error("Error fetching candidates:", error);
        setHasMoreCandidates(false);
      } finally {
        console.log("Candidates fetch complete, setting loading to false");
        setLoadingCandidates(false);
        setIsTabSwitching(false);
        isFetchingCandidates.current = false;
      }
    },
    [userData?.orgId, loadingCandidates]
  );

  const fetchMoreJobs = useCallback(
    async (currentOffset = 0, reset = false) => {
      if (isFetchingJobs.current) return;
      console.log("Fetching jobs, loading state:", loadingJobs);
      isFetchingJobs.current = true;
      if (!loadingJobs) setLoadingJobs(true);

      try {
        const result = await fetchJobsMeta({
          isActive: false,
          page: currentOffset,
          limit: DEFAULT_LIMIT,
        });

        const newJobs: Job[] = result?.data?.data || [];
        if (Array.isArray(newJobs) && newJobs.length > 0) {
          setJobs((prev) => (reset ? newJobs : [...prev, ...newJobs]));
          setHasMoreJobs(newJobs.length >= DEFAULT_LIMIT);
          setJobOffset(currentOffset + newJobs.length);
        } else {
          setHasMoreJobs(false);
        }
      } catch (err) {
        console.error("Failed to load jobs:", err);
        setError("Failed to load jobs.");
        setHasMoreJobs(false);
      } finally {
        console.log("Jobs fetch complete, setting loading to false");
        setLoadingJobs(false);
        setIsTabSwitching(false);
        isFetchingJobs.current = false;
      }
    },
    [loadingJobs]
  );

  // Handle tab switching with proper loading states
  const handleTabSwitch = (tab: ArchiveTab) => {
    if (tab === selectedTab && !isTabSwitching) {
      // If the tab is already selected, do nothing
      return;
    }
    console.log("Tab switching to:", tab);
    setSelectedTab(tab);
    setIsTabSwitching(true);

    if (tab === ARCHIVE_TABS.CANDIDATES) {
      console.log("Setting candidates loading to true");
      setLoadingCandidates(true);
      // Add small delay to ensure loading state is visible
      setTimeout(() => {
        fetchMoreCandidatesApplications(0, true);
      }, 100);
    } else if (tab === ARCHIVE_TABS.JOBS) {
      setLoadingJobs(true);
      // Add small delay to ensure loading state is visible
      setTimeout(() => {
        fetchMoreJobs(0, true);
      }, 100);
    }
  };

  // Initial load effect - only run once when component mounts
  useEffect(() => {
    if (userData?.orgId) {
      if (selectedTab === ARCHIVE_TABS.CANDIDATES && candidates.length === 0) {
        setLoadingCandidates(true);
        fetchMoreCandidatesApplications(0, true);
      } else if (selectedTab === ARCHIVE_TABS.JOBS && jobs.length === 0) {
        setLoadingJobs(true);
        fetchMoreJobs(0, true);
      }
    }
  }, [userData?.orgId]); // Only depend on userData?.orgId

  const handleArchiveJob = async (jobId: number) => {
    setActiveDropdown(null);
    try {
      setConfirmationModal((prev) => ({ ...prev, loading: true }));
      setJobs((prevJobs) => prevJobs.filter((job) => job.id !== jobId));
      const response = await updateJobStatus(jobId, true);

      if (response && response.data && response.data.success) {
        toastMessageSuccess(translate("job_restored_successfully"));
        closeConfirmationModal();
      } else {
        toastMessageError(translate("failed_to_restore_job"));
        closeConfirmationModal();
      }
    } catch {
      console.error(translate("archive_job_failed"));
      closeConfirmationModal();
    }
  };

  const handleRestoreCandidate = async (applicationId: number) => {
    try {
      setConfirmationModal((prev) => ({ ...prev, loading: true }));
      const response = await archiveActiveApplication(applicationId, true);
      if (response && response.data && response.data.success) {
        toastMessageSuccess(translate("restore_candidate_successfully"));
        closeConfirmationModal();
      } else {
        toastMessageError(translate("failed_to_restore_candidate"));
        closeConfirmationModal();
      }

      setCandidates((prev) => prev.filter((c) => c.applicationId !== applicationId));
    } catch (error) {
      console.error("Failed to restore candidate:", error);
    }
    closeConfirmationModal();
  };

  const toggleDropdown = (id: number) => {
    setActiveDropdown((prev) => (prev === id ? null : id));
  };

  useEffect(() => {
    // Reset dropdown when switching tabs
    setActiveDropdown(null);
  }, [selectedTab]);

  const handleToRestoreJob = async (id: number) => {
    handleArchiveJob(id);
  };

  return (
    <div className="container">
      <div className="common-page-header">
        <div className="common-page-head-section">
          <div className="main-heading">
            <h2>
              {t("hiring_manager_dashboard")} - <span>{t("archive_job")}</span>
            </h2>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="common-box">
        <main className="main-content">
          <div className={style.dashboard_page}>
            {hasAnyPermission ? (
              <div className={`${styles.dashboard_inner_head} mt-0`}>
                <ul className={styles.header_tab}>
                  {/* Only show Candidates tab if user has permission */}
                  {hasArchiveRestoreCandidatesPermission && (
                    <li
                      className={selectedTab === ARCHIVE_TABS.CANDIDATES ? styles.active : ""}
                      onClick={() => handleTabSwitch(ARCHIVE_TABS.CANDIDATES)}
                      style={{ cursor: "pointer" }}
                    >
                      {t("candidates")}
                    </li>
                  )}

                  {/* Only show Jobs tab if user has permission */}
                  {hasArchiveRestoreJobPermission && (
                    <li
                      className={selectedTab === ARCHIVE_TABS.JOBS ? styles.active : ""}
                      onClick={() => handleTabSwitch(ARCHIVE_TABS.JOBS)}
                      style={{ cursor: "pointer" }}
                    >
                      {t("jobs")}
                    </li>
                  )}
                </ul>
              </div>
            ) : (
              <div className="text-center mt-5">
                <h3 className="mt-3">{t("you_dont_have_permission_to_view_archive_content")}</h3>
              </div>
            )}

            {/* Only show content if user has any permission */}
            {hasAnyPermission && (
              <>
                {/* CANDIDATES - Only show if user has permission */}
                {selectedTab === ARCHIVE_TABS.CANDIDATES && hasArchiveRestoreCandidatesPermission && (
                  <div className="table-responsive mt-5" style={{ maxHeight: "60vh", overflowY: "auto" }} id="scrollableCandidateDiv">
                    {/* {console.log("Rendering candidates tab, loadingCandidates:", loadingCandidates, "candidates.length:", candidates.length)} */}
                    {(loadingCandidates || isTabSwitching) && !hasMoreCandidates ? (
                      <table className="table">
                        <thead>
                          <tr>
                            <th>{t("candidate_name")}</th>
                            <th>{t("archived_on")}</th>
                            <th>{t("reason_for_archiving")}</th>
                            <th className="text-center">{t("actions")}</th>
                          </tr>
                        </thead>
                        <TableSkeleton rows={5} cols={4} colWidths="120,80,100,24" />
                      </table>
                    ) : (
                      <InfiniteScroll
                        dataLength={candidates.length}
                        next={() => fetchMoreCandidatesApplications(candidateOffset)}
                        hasMore={hasMoreCandidates}
                        scrollableTarget="scrollableCandidateDiv"
                        height={window.innerHeight - 350}
                        loader={
                          loadingCandidates ? (
                            <table className="table w-100">
                              <TableSkeleton rows={3} cols={4} colWidths="120,80,100,24" />
                            </table>
                          ) : null
                        }
                        endMessage={
                          !loadingCandidates && candidates.length ? (
                            <table className="table w-100">
                              <tbody>
                                <tr>
                                  <td colSpan={5} style={{ textAlign: "center", backgroundColor: "#fff" }}>
                                    {t("no_more_candidates_to_fetch")}
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          ) : null
                        }
                      >
                        <table className="table w-100 overflow-auto mb-0">
                          <thead>
                            <tr>
                              <th>{t("candidate_name")}</th>
                              <th>{t("archived_on")}</th>
                              <th>{t("reason_for_archiving")}</th>
                              <th className="text-center">{t("actions")}</th>
                            </tr>
                          </thead>
                          {candidates.length ? (
                            <tbody>
                              {candidates.map((candidate) => {
                                const date = candidate.applicationCreatedTs ? new Date(candidate.applicationCreatedTs) : null;
                                const formattedDate =
                                  date && !isNaN(date.getTime())
                                    ? date.toLocaleDateString("en-US", {
                                        year: "numeric",
                                        month: "short",
                                        day: "numeric",
                                      })
                                    : "—";

                                return (
                                  <tr key={candidate.applicationId}>
                                    <td>{candidate.candidateName}</td>
                                    <td>{formattedDate}</td>
                                    <td>{candidate.hiringManagerReason || "—"}</td>
                                    <td className="text-center">
                                      <div className="position-relative">
                                        <Button className="clear-btn p-0" onClick={() => toggleDropdown(candidate.applicationId)}>
                                          <ThreeDotsIcon />
                                        </Button>
                                        {activeDropdown === candidate.applicationId && (
                                          <ul className="custom-dropdown show">
                                            <li
                                              onClick={() => {
                                                setConfirmationModal({
                                                  isOpen: true,
                                                  title: "Restore Candidate",
                                                  message: `Are you sure you want to restore candidate ${candidate.candidateName}?`,
                                                  confirmButtonText: t("restore"),
                                                  onConfirm: () => handleRestoreCandidate(candidate.applicationId),
                                                  loading: false,
                                                });
                                              }}
                                            >
                                              {t("restore")}
                                            </li>
                                          </ul>
                                        )}
                                      </div>
                                    </td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          ) : (
                            !loadingCandidates && (
                              <tbody>
                                <tr>
                                  <td colSpan={4} style={{ textAlign: "center" }}>
                                    <NoDataFoundIcon width={400} height={400} />
                                  </td>
                                </tr>
                              </tbody>
                            )
                          )}
                        </table>
                      </InfiniteScroll>
                    )}
                  </div>
                )}

                {/* JOBS - Only show if user has permission */}
                {selectedTab === ARCHIVE_TABS.JOBS && hasArchiveRestoreJobPermission && (
                  <>
                    {error && <p className="text-danger">{error}</p>}

                    {(loadingJobs || isTabSwitching) && !hasMoreJobs ? (
                        <div className="row g-4 pb-4 w-100">
                        {Array(6)
                          .fill(0)
                          .map((_, index) => (
                            <div key={`skeleton-job-${index}`} className="col-md-4">
                              <div className="jobs-card position-relative">
                                <div className="name d-flex justify-content-between align-items-center">
                                  <h4 className="w-100">
                                    <Skeleton width="60%" height={18} />
                                  </h4>
                                  <Skeleton circle width={24} height={24} />
                                </div>
                                <p className="description">
                                  <Skeleton width="100%" height={16} />
                                </p>
                              </div>
                            </div>
                          ))}
                      </div>
                    ) : (
                      <div className="row g-4 w-100 pb-4" id="scrollableJobDiv" style={{ maxHeight: "60vh", overflowY: "auto" }}>
                        <InfiniteScroll
                          dataLength={jobs.length}
                          next={() => fetchMoreJobs(jobOffset)}
                          hasMore={hasMoreJobs}
                          scrollableTarget="scrollableJobDiv" // Ensure this matches the container's id
                          loader={
                            loadingJobs ? (
                              <div className="row g-4 pb-4 w-100">
                                {Array(3)
                                  .fill(0)
                                  .map((_, index) => (
                                    <div key={`skeleton-job-${index}`} className="col-md-4">
                                      <div className="jobs-card position-relative">
                                        <div className="name d-flex justify-content-between align-items-center">
                                          <h4 className="w-100">
                                            <Skeleton width="60%" height={18} />
                                          </h4>
                                          <Skeleton circle width={24} height={24} />
                                        </div>
                                        <p className="description">
                                          <Skeleton width="100%" height={16} />
                                        </p>
                                      </div>
                                    </div>
                                  ))}
                              </div>
                            ) : null
                          }
                          endMessage={
                            !loadingJobs && jobs.length && jobs.length >= DEFAULT_LIMIT ? (
                              <p className="text-center mt-3">{t("no_more_archive_jobs_to_fetch")}</p>
                            ) : null
                          }
                        >
                          {jobs.length > 0 ? (
                            <div className="row g-4 mt-3 w-100 pb-4">
                              {jobs.map((job) => (
                                <div key={job.id} className="col-md-4">
                                  <div className="jobs-card position-relative">
                                    <div className="name d-flex justify-content-between align-items-center">
                                      <h4>{job.title}</h4>
                                      <div className="position-relative">
                                        <Button
                                          className="clear-btn p-0"
                                          onClick={() => {
                                            setConfirmationModal({
                                              isOpen: true,
                                              title: "Restore Job",
                                              message: "Are you sure you want to restore this job?",
                                              confirmButtonText: t("restore"),
                                              onConfirm: () => handleToRestoreJob(job.id),
                                              loading: false,
                                            });
                                          }}
                                        >
                                          <RestoreIcon />
                                        </Button>
                                      </div>
                                    </div>
                                    <p className="description">
                                      {job.jobId} |{" "}
                                      {(() => {
                                        const date = job.updatedDate ? new Date(job.updatedDate) : null;
                                        return date && !isNaN(date.getTime())
                                          ? date.toLocaleDateString("en-US", { year: "numeric", month: "short", day: "numeric" })
                                          : "—";
                                      })()}
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            !loadingJobs && (
                              <div className="text-center mt-3">
                                <NoDataFoundIcon width={400} height={400} />
                              </div>
                            )
                          )}
                        </InfiniteScroll>
                      </div>
                    )}
                  </>
                )}
              </>
            )}
            <ConfirmationModal
              isOpen={confirmationModal.isOpen}
              onClose={closeConfirmationModal}
              onConfirm={confirmationModal.onConfirm}
              title={confirmationModal.title}
              message={confirmationModal.message}
              confirmButtonText={confirmationModal.confirmButtonText}
              loading={confirmationModal.loading}
              loadingText={t("processing")}
            />
          </div>
        </main>
      </div>
    </div>
  );
}

export default Archive;
