"use client";
import React, { useEffect, useState } from "react";

import styles from "../../../styles/accessManagement.module.scss";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import SearchIcon from "@/components/svgComponents/SearchIcon";
import { useForm } from "react-hook-form";
import ThreeDotsIcon from "@/components/svgComponents/ThreeDotsIcon";
import FolderColorIcon from "@/components/svgComponents/FolderColorIcon";
// import UserRoleModal from "@/components/commonModals/UserRoleModal";
import DepartmentModal from "@/components/commonModals/DepartmentModal";
import { FindDepartmentResponse, findDepartments } from "@/services/departmentService";
import { useRouter } from "next/navigation";
import routes from "@/constants/routes";
import { toastMessageSuccess, toastMessageError } from "@/utils/helper";
import { useTranslations } from "next-intl";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { PERMISSION, SPACIAL_CHARACTERS, TYPING_PREVENTION_CHARACTERS } from "@/constants/commonConstants";
import { useHasPermission } from "@/utils/permission";
import { useTranslate } from "@/utils/translationUtils";
import NoDepartmentFound from "../../../../public/assets/images/operation-admins-img.png";
import Image from "next/image";
export const DEPARTMENT_ALTER_MODE = {
  ADD: "add",
  EDIT: "edit",
  DELETE: "delete",
};

const EmployeeManagement = () => {
  const router = useRouter();
  const { control } = useForm<{ search: string }>({ defaultValues: { search: "" } });
  const [searchValue, setSearchValue] = useState("");
  const t = useTranslations();
  const translate = useTranslate();

  // Permission checks
  const hasCreateNewDepartmentPermission = useHasPermission(PERMISSION.CREATE_NEW_DEPARTMENT);
  const hasAddEmployeePermission = useHasPermission(PERMISSION.ADD_EMPLOYEE);

  // const [roleModalConfig, setRoleModalConfig] = useState<{
  //   show: boolean;
  //   mode: (typeof EMPLOYEE_ALTER_MODE)[keyof typeof EMPLOYEE_ALTER_MODE];
  //   role: { id: number; name: string } | null;
  // }>({ show: false, mode: EMPLOYEE_ALTER_MODE.EDIT, role: null });

  const [departments, setDepartments] = useState<FindDepartmentResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeDepartmentMenu, setActiveDepartmentMenu] = useState<number | null>(null);

  const [departmentModalConfig, setDepartmentModalConfig] = useState<{
    show: boolean;
    mode: (typeof DEPARTMENT_ALTER_MODE)[keyof typeof DEPARTMENT_ALTER_MODE];
    department: FindDepartmentResponse | null;
  }>({ show: false, mode: DEPARTMENT_ALTER_MODE.ADD, department: null });

  // Function to prevent typing restricted characters
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Block %, _, and \ characters
    if (TYPING_PREVENTION_CHARACTERS.includes(e.key)) {
      e.preventDefault();
    }
  };
  // Fetch departments on component mount and when search value changes
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchDepartments(searchValue);
    }, 500); // Debounce search for 500ms

    return () => clearTimeout(debounceTimer);
  }, [searchValue]);

  // Function to fetch departments
  const fetchDepartments = async (search?: string) => {
    try {
      setIsLoading(true);
      const response = await findDepartments(search);
      const result = response.data;

      if (result && result.success && result.data) {
        // Check if departments is directly in data or nested
        if (Array.isArray(result.data)) {
          setDepartments(result.data);
        } else {
          toastMessageError(translate(result.message || "failed_load_departments"));
          setDepartments([]);
        }
      } else {
        toastMessageError(translate(result.message || "failed_load_departments"));
        setDepartments([]);
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("error_fetching_departments"));
      setDepartments([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to open department modal
  const openDepartmentModal = (
    mode: (typeof DEPARTMENT_ALTER_MODE)[keyof typeof DEPARTMENT_ALTER_MODE],
    department: FindDepartmentResponse | null = null
  ) => {
    setDepartmentModalConfig({
      show: true,
      mode,
      department,
    });
  };

  // Function to close department modal
  const closeDepartmentModal = () => {
    setDepartmentModalConfig({
      show: false,
      mode: DEPARTMENT_ALTER_MODE.ADD,
      department: null,
    });
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if the click was outside any department action menu
      const target = event.target as HTMLElement;
      if (!target.closest(".department-actions")) {
        setActiveDepartmentMenu(null);
      }
    };

    // Add the event listener
    document.addEventListener("mousedown", handleClickOutside);

    // Clean up
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  return (
    <>
      <section className={styles.access_management}>
        <div className="container">
          <div className="row">
            <div className="col-md-12">
              <div className="common-page-header">
                <div className="common-page-head-section">
                  <div className="main-heading">
                    <h2>
                      Employee <span>Management</span>
                    </h2>
                    <div className="right-action">
                      <InputWrapper className="mb-0 w-100">
                        <div className="icon-align right">
                          <Textbox
                            className="form-control w-100"
                            control={control}
                            name="search"
                            type="text"
                            placeholder={t("search_department")}
                            onKeyDown={handleKeyDown}
                            onPaste={(e) => {
                              const pastedText = e.clipboardData.getData("text");
                              if (TYPING_PREVENTION_CHARACTERS.some((char) => pastedText.includes(char))) {
                                e.preventDefault();
                              }
                            }}
                          >
                            <InputWrapper.Icon>
                              <SearchIcon />
                            </InputWrapper.Icon>
                          </Textbox>
                        </div>
                      </InputWrapper>

                      {hasCreateNewDepartmentPermission && (
                        <Button className="primary-btn rounded-md button-sm" onClick={() => openDepartmentModal(DEPARTMENT_ALTER_MODE.ADD)}>
                          {t("add_new_department")}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              {isLoading ? (
                <ul className="library-folder mt-0 w-100">
                  {Array.from({ length: 12 }).map((_, index) => (
                    <li key={index} className="folder-card p-0 border-0  ">
                      <Skeleton height={165} width="100%" borderRadius={10} inline={true} />
                    </li>
                  ))}
                </ul>
              ) : (
                <ul className="library-folder mt-0 w-100">
                  {departments.map((department) => (
                    <li className="folder-card" key={department.id}>
                      <div className="position-relative">
                        <div className="department-actions">
                          {hasCreateNewDepartmentPermission && (
                            <>
                              <Button
                                className={`clear-btn p-0 ${department.isDefaultDepartment ? "disabled opacity-0" : ""}`}
                                onClick={(e) => {
                                  if (!department.isDefaultDepartment) {
                                    e.stopPropagation(); // Prevent event bubbling
                                    setActiveDepartmentMenu(activeDepartmentMenu === department.id ? null : department.id);
                                  }
                                }}
                                disabled={department.isDefaultDepartment}
                              >
                                <ThreeDotsIcon />
                              </Button>

                              {activeDepartmentMenu === department.id && (
                                <div className={styles.custom_dropdown}>
                                  <div
                                    className={styles.dropdown_item}
                                    onClick={() => {
                                      openDepartmentModal(DEPARTMENT_ALTER_MODE.EDIT, department);
                                      setActiveDepartmentMenu(null);
                                    }}
                                  >
                                    {t("edit")}
                                  </div>
                                  <div className={styles.dropdown_divider}></div>
                                  <div
                                    className={styles.dropdown_item}
                                    onClick={() => {
                                      openDepartmentModal(DEPARTMENT_ALTER_MODE.DELETE, department);
                                      setActiveDepartmentMenu(null);
                                    }}
                                  >
                                    {t("delete")}
                                  </div>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                      <div
                        className={styles.folder_container}
                        onClick={() => {
                          if (hasAddEmployeePermission) {
                            router.push(
                              `${routes.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT_DETAIL}?departmentId=${department.id}&departmentName=${encodeURIComponent(department.name)}`
                            );
                          }
                        }}
                        style={{ cursor: hasAddEmployeePermission ? "pointer" : "default" }}
                      >
                        <FolderColorIcon className="folder-icon" />
                        <p className={styles.department_card} title={department.name}>
                          {department.name.length > 13 ? department.name.substring(0, 13) + "..." : department.name}
                        </p>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
              {departments.length === 0 && !isLoading && (
                <div className="d-flex justify-content-center w-100">
                  <div className="text-center py-5">
                    <Image src={NoDepartmentFound} className="img-fluid w-50" alt="No Department Found" />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
      {/* 
      {roleModalConfig.show && (
        <UserRoleModal
          onClickCancel={() => setRoleModalConfig({ show: false, mode: EMPLOYEE_ALTER_MODE.EDIT, role: null })}
          onSubmitSuccess={(message) => { 
            if (message) {
              toastMessageSuccess(message);
            } else {
              toastMessageSuccess(`Role ${roleModalConfig.mode === EMPLOYEE_ALTER_MODE.ADD ? "created" : roleModalConfig.mode === EMPLOYEE_ALTER_MODE.EDIT ? "updated" : "deleted"} successfully`);
            }
          }}
          role={roleModalConfig.role}
          mode={roleModalConfig.mode}
          disabled={isLoading}
        />
      )} */}

      {departmentModalConfig.show && (
        <DepartmentModal
          onClickCancel={closeDepartmentModal}
          onSubmitSuccess={(message) => {
            fetchDepartments();
            // Show toast notification if message is provided
            if (message) {
              toastMessageSuccess(message);
              setSearchValue("");
            }
          }}
          department={departmentModalConfig.department}
          mode={departmentModalConfig.mode}
        />
      )}
    </>
  );
};

export default EmployeeManagement;
