import endpoint from "@/constants/endpoint";
import {
  IForgotPassword,
  IResendOTP,
  IResetPassword,
  ISendVerificationEmail,
  IVerifyOTP,
  UserPermissionsResponse,
} from "@/interfaces/authInterfaces";
import * as http from "@/utils/http";
import { IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";

export const verifyOTP = (data: IVerifyOTP): Promise<IApiResponseCommonInterface<string>> => {
  return http.post(endpoint.auth.VERIFY_OTP, data);
};

export const resendOTP = (data: IResendOTP): Promise<IApiResponseCommonInterface<null>> => {
  return http.post(endpoint.auth.RESEND_OTP, data);
};

export const forgotPassword = (data: IForgotPassword): Promise<IApiResponseCommonInterface<null>> => {
  return http.post(endpoint.auth.FORGOT_PASSWORD, data);
};

export const resetPassword = (data: IResetPassword): Promise<IApiResponseCommonInterface<null>> => {
  return http.post(endpoint.auth.RESET_PASSWORD, data);
};

export const deleteSession = (userId?: number): Promise<IApiResponseCommonInterface<null>> => {
  return http.remove(`${endpoint.auth.DELETE_SESSION}/${userId}`);
};

export const updateTimezone = (data: { timezone: string }): Promise<IApiResponseCommonInterface<null>> => {
  return http.post(endpoint.auth.UPDATE_TIMEZONE, data);
};

export const getUserPermissions = (): Promise<IApiResponseCommonInterface<UserPermissionsResponse>> => {
  return http.get(endpoint.roles.USER_PERMISSIONS);
};

export const checkUserOrgExist = (data: {
  organizationCode: string;
  organizationName: string;
  firstName: string;
  email: string;
}): Promise<IApiResponseCommonInterface<{ userAlreadyExistWithoutOrg: boolean } | null>> => {
  return http.post(endpoint.auth.CHECK_USER_ORG_EXIST, data);
};

export const sendVerificationEmail = (data: ISendVerificationEmail): Promise<IApiResponseCommonInterface<null>> => {
  return http.post(endpoint.auth.SEND_VERIFICATION_EMAIL, data);
};

// export const getPredictions = (searchText: string): Promise<ApiResponse> => {
// 	return http.get(
// 		`${endpoint.auth.SEARCH_PREDICTION.replace(":searchText", searchText)}`
// 	);
// };
