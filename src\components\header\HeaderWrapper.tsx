"use client";
import { usePathname } from "next/navigation";
import Header from "@/components/header/Header";
import ROUTES, { BEFORE_LOGIN_ROUTES } from "@/constants/routes";
import HomeHeader from "./HomeHeader";

export default function HeaderWrapper() {
  const pathname = usePathname();

  console.log("Header wrapper pathname", pathname);
  if (ROUTES.HOME === pathname) {
    // show before login header if user is on before login page
    return <HomeHeader />;
  } else if (BEFORE_LOGIN_ROUTES.includes(pathname)) {
    return null;
  }

  // show after login header if user is not on before login page
  return <Header />;
}
