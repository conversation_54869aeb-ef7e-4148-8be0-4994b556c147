@use "./abstracts" as *;

.header {
  background: $white;
  backdrop-filter: blur(10px);

  // Profile image fallback styles for header
  .header_profile_image_fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #ecf1f8;
    border-radius: 50%;
    font-size: 16px;
    font-weight: bold;
    color: $dark;
    border: 1px solid #ecf1f8;
  }
  box-shadow:
    0px 24px 7px 0px rgba(0, 0, 0, 0),
    0px 15px 6px 0px rgba(0, 0, 0, 0.01),
    0px 9px 5px 0px rgba(0, 0, 0, 0.02),
    0px 4px 4px 0px rgba(0, 0, 0, 0.03),
    0px 1px 2px 0px rgba(0, 0, 0, 0.04),
    0px 0px 0px 0px rgba(0, 0, 0, 0.04);
  z-index: 999;
  position: sticky;
  top: 0;
  left: 0;
  &.hidden {
    transform: translateY(-100%);
  }
  .logo {
    width: 180px;
    height: 70px;
    object-fit: contain;
    @media (max-width: 991px) {
      width: 150px;
      height: 50px;
    }
  }
  nav {
    padding-block: 5px;
  }
  .navbar_content {
    gap: 3rem;
    flex-grow: 0;

    .navbar_links {
      gap: 2rem;
      width: 100%;
      justify-content: center;

      li {
        a {
          font-size: $text-md;
          color: $dark;
          position: relative;
          text-align: center;
          font-weight: $medium;
          &.active,
          &:hover {
            color: $primary;
          }
          &:active {
            opacity: 0.7;
          }
        }
      }
    }
    .align_header_search {
      display: flex;
      align-items: center;
      gap: 2.5rem;
      button {
        svg {
          width: 18px;
          height: 18px;
        }
      }
      .search_wrapper {
        display: flex;
        align-items: center;
        background: #f5f5f5;
        border-radius: 8px;
        input {
          background: transparent;
          border: none;
          font-size: $text-sm;
          padding: 0 10px;
          &:focus {
            outline: none;
            box-shadow: none;
          }
          &::placeholder {
            color: rgba($dark, 0.5);
          }
        }
      }
      .header_buttons {
        gap: 2.5rem;
        button {
          font-weight: 400;
        }
      }

      .search_wrapper {
        display: none;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;

        &.show {
          display: flex;
          opacity: 1;
        }
      }
    }
  }
}
.home_header {
  z-index: 999;
  position: sticky;
  top: 0;
  left: 0;
  background: transparent;
  transition: background 0.3s ease;

  .logo {
    width: 180px;
    height: 70px;
    object-fit: contain;

    @media (max-width: 991px) {
      width: 150px;
    }
  }
  /* Dark overlay */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.4);
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease;
    z-index: 1000;

    &.show {
      opacity: 1;
      visibility: visible;
    }
  }

  /* Right side sliding menu */
  .sideMenu {
    position: fixed;
    top: 0;
    right: -280px; // hidden by default
    width: 280px;
    height: 100vh;
    background: $white;
    box-shadow: -4px 0 12px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 80px 24px 24px; // space for header
    transition: right 0.4s ease;
    z-index: 1001;

    &.open {
      right: 0;
    }

    button {
      width: 100%;
    }
  }

  /* Desktop Buttons */
  .desktopButtons {
    // display: flex;
    // gap: 12px;

    @media (max-width: 767px) {
      display: none; // hide on mobile
    }
  }

  /* Side Menu (mobile only) */
  .sideMenu {
    @media (min-width: 768px) {
      display: none; // hide on desktop
    }
  }
  /* Hamburger */
  .hamburgerSM {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 40px;
    height: 35px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    z-index: 1002;
    backdrop-filter: blur(10px);
    // background: $primary;
    border-radius: 5px;

    span {
      height: 3px;
      width: 100%;
      background: $primary;
      border-radius: 2px;
      transition: all 0.4s ease;
      transform-origin: center;
    }

    &.active {
      span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
        background: $primary;
      }
      span:nth-child(2) {
        opacity: 0;
      }
      span:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
        background: $primary;
      }
    }

    @media (max-width: 767px) {
      display: flex;
      position: absolute;
      right: 25px;
      top: 25px;
    }
  }
}

.searchContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.searchBar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  overflow: hidden;
  border: 1px solid $primary;
  border-radius: 20px;
  background: transparent;
  transition: width 0.3s ease-in-out;
  button {
    padding: 10px !important;
  }
  &.open {
    width: 300px;
  }
}

.searchButton {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.searchIcon {
  width: 20px;
  height: 20px;
  color: #555;
}

.searchInput {
  width: 0;
  padding: 0;
  border: none;
  outline: none;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  background: transparent;
  color: $white;
  & > div {
    width: 100%;
  }
  input {
    background: transparent;
    border: none;
    color: $white;
    width: 100%;
    &:focus {
      outline: none;
      color: $white;
    }
  }
  .searchBar.open & {
    opacity: 1;
    width: 100%;
    padding: 8px;
  }
}
.header_right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 2rem;
  @media (max-width: 1199px) and (min-width: 992px) {
    gap: 5px;
  }

  @media (max-width: 991px) {
    display: none;
  }
}

// dropdown menu style
.user_drop {
  display: flex;
  justify-content: flex-end;
  @media (max-width: 991px) {
    display: none;
  }
  .user_drop_btn {
    display: flex;
    align-items: center;
    border: 0px;
    background-color: transparent;
    color: $dark;
    min-width: 135px;
    border-radius: 10px 10px 0px 0px;
    font-size: 14px;
    line-height: normal;
    // font-weight: $bold;
    gap: 10px;
    .admin_info {
      h5 {
        font-size: $text-md;
        margin-bottom: 3px;
        text-align: left;
      }
      p {
        text-align: left;
        font-size: 1.2rem;
      }
    }

    .circle_img {
      // margin-right: 10px;
      margin-right: 0px;
      img {
        width: 40px;
        height: 40px;
        min-width: 40px;
        border-radius: 100px;
        object-fit: cover;
      }
    }
    svg {
      margin-left: 10px;
      width: 14px;
      min-width: 14px;
    }
    &:after {
      display: none;
    }
  }

  .dropdown_menu {
    border-radius: 0px 0px 8px 8px;
    background: #fff;
    padding: 10px 15px;
    backdrop-filter: blur(11px);
    min-width: 250px;
    position: relative;
    z-index: 10;
    position: absolute;
    top: 50px;
    border: none;
    display: block;
    z-index: 999;
    box-shadow: 0 2px 8px 0 rgba(99, 99, 99, 0.2);
    border-radius: 10px;
    padding-top: 15px;
    .sidebar_sub_menu {
      padding-left: 28px;
      margin: 0;
      li {
        font-size: 1.4rem;
        color: rgba($dark, 0.8);
        padding-top: 5px;
        margin-bottom: 0;
        display: flex;
        align-items: center;
        gap: 6px;
        svg {
          width: 16px;
          height: 16px;
        }
      }
    }
    li {
      border-bottom: 1px dashed rgba($dark, 0.08);
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;
      padding-bottom: 10px;
      cursor: pointer;
      font-size: 1.6rem;
      color: $dark;
      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }
      ul {
        padding-top: 5px;
        li {
          border-bottom: none;
          padding: 3px 0;
          svg {
            fill: #fff !important;
            min-width: 18px;
          }
        }
      }
      svg {
        width: 20px;
        height: 20px;
        fill: $dark;
        min-width: 22px;
      }

      &.sub_menubar {
        flex-direction: column;
        align-items: flex-start;
        gap: 0;
        .sub_menu_list {
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }
      &:active {
        opacity: 0.7;
      }
      &:hover {
        color: $primary;
        svg {
          fill: $primary;
        }
      }
    }
  }
}
