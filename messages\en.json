{"feedback_already_submitted": "Feedback already submitted", "upload_here": "Upload here", "invalid_email": "Invalid email", "create_new_event": "Create New Event", "title_req": "Title is required", "date_req": "Date is required", "start_time_req": "Start time is required", "end_time_req": "End time is required", "interview_type_req": "Interview type is required", "start_interview": "Start Interview", "job_id_desc": "Job ID", "all_events": "All Events", "dont_have_an_account": "Don't have an account?", "choose_password": "Choose Password", "please_select_job": "Please select job", "already_have_account": "Already have account?", "organization_name_already_exist": "Organization name already exist", "organization_code_already_exist": "Organization code already exist", "user_already_associated_with_other_organization": "User already associated with other organization", "sign_up": "Sign Up", "signup_successful": "Signup successful", "org_code": "Org Code", "tin_number": "TIN Number", "branch_name": "Branch Name", "branch_code": "Branch Code", "continue_with_existing_password": "Continue with existing password", "continue_with_new_password": "Continue with new password", "location": "Location", "website_url": "Website URL", "job_title": "Job Title", "interviewer_disconnected": "The Interviewer has been disconnected. This session will end soon.", "pick_a_question": "Pick A Question", "all_skills_locked": "All skills in this category are already completed", "proceed": "Proceed", "conducting_interviews": "Conducting Interviews", "update_interview": "Update Interview", "interview_round_number": "Interview Round Number", "view_interview_questions": "View Interview Questions", "pending_feedbacks": "Pending Feedbacks", "candidate_placeholder": "Please select candidate", "view_your_feedback": "View Your Feedback", "no_pending_interviews_found": "No pending interviews found that require feedback.", "pending_analysis": "Pending Analysis", "behavioral_info_max_1500_chars": "Behavioral info must be at most 1500 characters long", "job_application_is_not_approved": "Job application is not approved", "answer_max_2000_chars": "Answer must be at most 2000 characters long", "interview_date": "Interview Date", "interview_type": "Interview Type", "follow_up": "Follow Up:", "interview_duration": "Interview Duration", "interview_feedback_is_not_filled_by_interviewer_yet": " Interview feedback is not filled by interviewer yet", "advance_candidate_title": "Advance Candidate", "advance_candidate_message": "Are you sure you want to advance this candidate to the next round? This action cannot be undone.", "advance_button": "Advance", "reject_candidate_title": "Reject Candidate", "reject_candidate_message": "Are you sure you want to reject this candidate? This action cannot be undone.", "reject_button": "Reject", "processing_text": "Processing...", "feedback_submit_success_advance": "Candidate moved to next round.", "feedback_submit_success_reject": "Candidate marked as Rejected.", "feedback_submit_error": "Failed to submit feedback", "feedback_submit_error_generic": "Something went wrong while submitting your feedback. Please try again.", "feedback_fetch_error": "Failed to fetch interview feedback", "feedback_load_error": "Something went wrong while loading interview feedback", "feedback_fields_required": "Please fill in existing required fields before adding a new one", "job_title_req": "Job title is required", "please_select_job_title_first": "Please select job title first", "job_id_req": "Job ID is required", "candidate_req": "Candidate is required", "upload_doc": "Upload Document In PDF Format", "please_select_interview_type": "Please select interview type", "interview_is_ended": "Interview is already ended", "uploading": "Uploading...", "max_file_size": "maximum file size 10MB", "title": "Title", "describe_candidate_behaviours": "Describe Candidate’s Behaviours & Other Observations", "loading": "Loading...", "date": "Date", "document": "Document", "home": "Home", "name_validation_requirements": "name: 3-50 letters only, no numbers or special characters allowed", "enter_question": "Enter Question", "add_question": "Add Question", "update_question": "Update Question", "conduct_interview": "Conduct Interview", "meeting_already_in_progress": "You can only participate in one interview at a time. Please exit your current interview before joining the new one.", "start_time": "Start Time", "confirm_leave": "Confirm to End the Interview?", "leave_interview_confirmation": "Your progress will be saved, and you will exit the session. Interview feedback will be generated within 5–10 minutes. An email notification will be sent once it’s ready, and next interview can be scheduled after submitting the feedback form.", "invalid_file_format": "Invalid file format", "invalid_size_format": "File size should be 10MB or less", "invalid_image_size_format": "Image size should be 5MB or less", "end_time": "End Time", "waiting_for_interviewer": "Waiting for interviewer...", "connection_status": "Connection Status: ", "interviewer_req": "Interviewer is required", "please_select_interviewer": "Please select interviewer", "please_select_date": "Please select date", "cannot_add_interview_skill_question_after_ended": "Cannot add interview skill question after ended", "enter_start_time": "Enter start time", "cannot_update_interview_after_ended": "Cannot update interview after ended", "enter_end_time": "Enter end time", "interview_ended": "Interview ended successfully", "cannot_schedule_more_than_one_month_in_advance": "Cannot schedule more than one month in advance", "description_max_2000_chars": "Description must be at most 2000 characters long", "end_time_must_be_after_start_time": "End time must be after start time", "interview_must_be_at_least_10_min": "Interview must be at least 10 minutes long", "interview_must_not_exceed_2_hours": "Interview must not exceed 2 hours", "previously_scheduled_interview_is_not_ended": "Previously scheduled interview is not ended", "interview_not_found": "Interview not found", "add_new_question": "+ Add New Question", "switch_category": "Switch Category", "career": "Career", "save_next": "Save & Next", "role_based": "Role Based", "culture_based": "Culture Based", "candidate_not_aware": "Can<PERSON><PERSON> is not aware of this skill or behaviour and don't understand how it impacts his performance.", "career_based": "Career-Based", "score_range": "from 1 to 9. Give extreme as a score for a candidate who far exceeds your expectations for this skill.", "score": "Score", "stratum": "Stratum", "score_the_candidate_for": "Score the candidate for", "career_based_skills": "Career-based skills", "title_min_5_chars": "Title must be at least 5 characters long", "title_max_100_chars": "Title must be at most 100 characters long", "edit_question": "Edit Question", "interview_description": "Enter event description", "title_desc": "Enter interview title", "additional_info_desc_": "Enter any additional info", "record_interview": "Record Interview", "culture_specific_performance_based_skills": "Culture Specific Performance-Based Skills", "role_specific_performance_based_skills": "Role Specific Performance-Based Skills", "for": "For", "extreme": "Extreme", "follow_up_generated": "Follow-Up {type} Generated Via AI", "generated_follow_up_skill_name": "The candidate mentioned {skill<PERSON>ame} - See the follow-up question!", "please_mark_stratum_score": "Please mark stratum score", "view_resume": "View Resume", "recording_in_progress": "Recording In-progress", "additional_document": "Additional Document", "interview_questions": "Interview Questions", "download_questions": "Download Questions", "interview_question_updated": "Interview question updated successfully", "interview_question_added": "Interview question added successfully", "question_not_found": "Question not found", "attachments": "Attachments", "stop_recording": "Stop Recording", "additional_notes": "Additional Notes", "question_req": "Question is required", "question_min_10_chars": "Question must be at least 10 characters long", "pre_interview_description": "Below, you'll find questions tailored for career-based, performance-based, and culture-based skills. Feel free to edit these questions or add new ones to fit your hiring needs.", "calendar": "Calendar", "dashboard_": "Dashboard", "question": "Question", "edit_details": "Edit Details", "invalid_or_malformed_url_parameters": "Invalid or malformed URL parameters", "end_interview": "End Interview", "end_interview_desc": "You can either qualify this candidate for the next round or you can end the interview process for this candidate. Once submitted you cannot reverse your choice.", "question_max_500_chars": "Question must be at most 500 characters long", "pre_interview": "Pre-Interview", "questions_overview": "Questions Overview", "interview_updated_successfully": "Interview updated successfully", "interview_is_not_allowed_for_next_round": "This candidate is not approved for next round", "description": "Description", "max_questions_reached": "You have reached maximum limit to add new questions", "job_requirement_generations": "Job Requirement Generations", "one_on_one": "In-Person", "video_call": "Online Interview", "ensure_smooth_interview": "Ensure a Smooth and Professional Interview Experience", "best_practice_for": "Best Practices for ", "cannot_schedule_interview_past": "Cannot schedule interview for past date", "max_files_limit_msg": "Maximum file upload limit is 3 files", "hiring_manager_dashboard": "Hiring Manager Dashboard", "schedule_interview": "Schedule Interview", "archive_candidate": "Archive Candidate", "cancel": "Cancel", "continue": "Continue", "proceed_to_interview": "Proceed To Interview", "interview_scheduled_successfully": "Interview scheduled successfully", "interview_already_scheduled": "Interview is already scheduled", "email_req_m": "Please enter your registered email", "email_val_msg": "Please enter a valid email address", "pass_not_match": "Password does not match", "pass_req": "Password is required", "organization_code_req": "Organization code is required", "location_req": "Location is required", "organization_name_req": "Organization name is required", "pass_val": "Password must be between 8 and 16 characters long and include at least 1 uppercase letter, 1 lowercase letter, 1 numeric digit, and 1 special character", "confirm_pass_req": "Please re-enter your password", "otp_req": "Please enter the received verification code", "valid_otp": "Please enter valid OTP", "user_not_found": "user does not exist", "please_enter_a_valid_verification_code": "Please enter a valid verification code", "otp_has_expired_please_request_a_new_otp": "Entered verification code is expired! You can request a new one.", "something_went_wrong": "Something went wrong", "verification_code_sent": "We sent a 4-digit verification code to your registered email", "otp_sending_failed": "Verification code sending failed", "wrong_password": "Email or password is incorrect", "login_successful": "Login successful", "assessment_has_expired": "Assessment link has expired", "assessment_instructions": "Assessment Instructions", "please_read_the_following_instructions_carefully": "Please read the following instructions carefully", "all_questions_are_mandatory_to_answer": "All questions are mandatory to answer", "once_submitted_you_cannot_change_your_answers": "Once submitted, you cannot change your answers", "please_complete_the_assessment_in_one_sitting": "Please complete the assessment in one sitting", "ensure_you_have_a_stable_internet_connection": "Ensure you have a stable internet connection", "you_must_provide_a_valid_email_address_to_proceed": "You must provide a valid email address to proceed", "email_address": "Email Address", "verifying": "Verifying...", "start_assessment": "Start Assessment", "email_verified_successfully": "Email verified successfully", "failed_to_verify_email": "Failed to verify email", "an_unexpected_error_occurred_while_verifying_email": "An unexpected error occurred while verifying email", "no_final_assessment_id_found": "No assessment ID found", "password_updated": "Password updated successfully", "skill_name_required": "Skill name is required", "skill_description_required": "Description is required", "no_career_skills": "No career skills data available. Please generate job skills first.", "generate_job_requirement_failed": "Failed to generate job requirement", "generate_job_requirement_error": "Something went wrong. Please try again.", "no_description_available": "No description available", "error_fetching_skills": "Error fetching skills data", "form_submission_error": "Form Submission Error", "pdf_job_req_empty": "Job requirement cannot be empty. Please add content before downloading.", "pdf_generating": "Generating PDF...", "pdf_download_success": "PDF downloaded successfully!", "pdf_download_fail": "Failed to generate PDF. Please try again.", "pdf_generation_error": "PDF generation error", "assessment_data_is_missing": "Assessment data is missing", "please_answer_all_questions_before_submitting": "Please answer all questions before submitting", "assessment_submitted_successfully": "Assessment submitted successfully", "failed_to_submit_assessment": "Failed to submit assessment", "an_error_occurred_while_submitting_assessment": "An error occurred while submitting assessment", "loading_assessment_questions": "Loading assessment questions...", "no_assessment_data_found": "No assessment data found. Please check the assessment ID.", "thank_you": "Thank You!", "your_assessment_has_been_submitted_successfully": "Your assessment has been submitted successfully.", "we_will_review_your_answers_and_get_back_to_you_soon": "We will review your answers and get back to you soon.", "candidate": "Candidate", "assessment": "Assessment", "your_information": "Your Information", "previous_skill_assessment": "Previous Skill Assessment", "submitting": "Submitting...", "submit_assessment": "Submit Assessment", "invalid_or_missing_final_assessment_id": "Invalid or missing finalAssessmentId", "job_id_and_job_application_id_are_required": "Job ID and Job Application ID are required", "copy_job_req_empty": "Job requirement cannot be empty. Please add content before copying.", "copy_html_success": "Formatted content copied to clipboard!", "copy_text_success": "Content copied to clipboard (plain text only)!", "copy_success": "Content copied to clipboard!", "copy_fail": "Failed to copy content", "copy_error": "Copy error", "fallback_copy_error": "Fallback copy error", "save_job_empty_error": "Job requirement cannot be empty. Please add content before saving.", "save_job_failed": "Failed to save job requirement", "limit_expired": "Limit expired", "save_job_unknown_error": "Something went wrong. Please try again.", "save_job_error_log": "Save job error", "error_fetching_jobs": "Error fetching jobs", "archive_job_failed": "Failed to archive the job", "error_fetching_applications": "Error fetching applications", "invalid_job_params": "Invalid job parameters. Redirecting to Active Jobs.", "set_view_questionarie": "View/Set Questionnaire", "view_your_summary": "View Your Summary", "error_fetching_interviews": "Error fetching interviews", "error_fetching_dashboard_count": "Error fetching dashboard count", "final_assessment_created_successfully": "Final assessment created successfully", "failed_to_create_final_assessment": "Failed to create final assessment", "an_error_occurred_while_creating_the_final_assessment": "An error occurred while creating the final assessment", "interview_summary": "Interview Summary", "interview_summary_summary": "Summary", "processing": "Loading...", "generating_final_assessment": "Generating Final Assessment...", "create_final_assessment": "Create Final Assessment", "view_final_assessment_result": "View Final Assessment Result", "view_final_assessment": "View Final Assessment", "round_1_summary": "ROUND 1 Summary", "preview_candidate_resume": "Preview Candidate Resume", "aaron_salko": "<PERSON>", "benedict_cumberbatch": "<PERSON>", "operations_admin": "Operations Admin", "department_add": "Add Department", "department_update": "Update Department", "department_delete": "Delete Department", "invalid_time_format": "Invalid time format", "department_default": "Department", "department_name": "Department Name", "department_name_placeholder": "Enter department name", "department_delete_confirmation": "Are you sure you want to delete the department \"{name}\"?", "department_added": "Department added successfully", "department_updated": "Department updated successfully", "department_deleted": "Department deleted successfully", "department_operation_failed": "Failed to {operation} department. Please try again.", "department_auth_error": "Authentication error. Please refresh the page and try again.", "department_unexpected_error": "An unexpected error occurred. Please try again.", "department_name_change_hint": "Change the department name to enable update", "cancel_department_edit": "Cancel", "final_assessment": "Final Assessment", "add_employees": "Add Employees", "employee_number": "Employee {number}", "remove_employee": "Remove employee", "first_name": "First Name", "create_account": "Create Account", "last_name": "Last Name", "email": "Email", "firstname_req": "First name is required", "lastname_req": "Last name is required", "min_length": "Characters or integer must be at least 3 long", "min_org_length": "Characters or integer must be at least 6 long", "max_length": "Characters or integer length can not be more then 50", "max_org_length": "Characters or integer length can not be more then 10", "max_domain_length": "URL length cannot be more than 255 characters", "invalid_domain_format": "URL can only contain letters, numbers, dots, and hyphens", "max_org_name_length": "Organization name cannot be more than 150 characters", "invalid_org_name_format": "Organization name can only contain letters, numbers, and spaces", "invalid_tin_format": "TIN Number must be exactly 9 digits", "invalid_branch_code_format": "Branch Code must be 3-6 alphanumeric characters without spaces", "invalid_branch_name_format": "Branch Name must be 3-100 characters with only letters, numbers, spaces, hyphens, underscores, and slashes", "min_lname": "Last name must be at least 1 character", "max_lname": "Last name must be at most 16 characters", "enter_your_first_name": "Enter your first name", "enter_your_last_name": "Enter your last name", "enter_tin_number": "Enter TIN number", "enter_branch_name": "Enter branch name", "enter_branch_code": "Enter branch code", "enter_location": "Enter your location", "enter_website_url": "Enter Website URL", "enter_org_code": "Enter organization code", "enter_organization_name": "Enter organization name", "department": "Department", "role": "Role", "order_of_interview": "Order of Interview", "enter_first_name": "Enter First Name", "enter_last_name": "Enter Last Name", "enter_email": "<PERSON><PERSON>", "select_department": "Select Department", "select_role": "Select Role", "enter_order_of_interview": "Enter order of interview", "loading_departments": "Loading departments...", "loading_roles": "Loading roles...", "add_another_employee": "+ Add Another Employee", "adding": "Adding...", "employees_added": "Employees added successfully!", "failed_load_roles": "Failed to load roles. Please try again.", "failed_load_departments": "Failed to load departments. Please try again.", "failed_load_data": "Failed to load required data. Please refresh the page.", "failed_add_employees": "Failed to add employees", "unexpected_error": "An unexpected error occurred. Please try again.", "no_final_assessment_id_found_in_url": "Invalid Assessment", "failed_to_fetch_assessment_questions": "Failed to fetch assessment questions", "an_error_occurred_while_fetching_assessment_questions": "An error occurred while fetching assessment questions", "employee_management": "Employee Management", "employee_management_heading": "Employee Management", "search_placeholder": "Search using name, user role", "search_department": "Search department", "employee_add_btn": "Add Employees", "update_order_of_interview": "Update Order of Interview", "name": "Name", "user_role": "User Role", "interview_order": "Order of Interview", "search_using_jobId_jobTitle": "Search using job id, job title", "no_more_jobs_to_fetch": "No more active jobs", "no_more_archive_jobs_to_fetch": "No more archived jobs", "job_id": "Job ID", "posted_on": "Posted Date", "application_submitted": "Application Submitted", "actions": "Actions", "loading_employees": "Loading employees...", "failed_load_employees": "Failed to load employees. Please try again.", "error_fetching_employees": "Error fetching employees", "retry_employee_load": "Retry", "no_employees_found": "No employees found in this department.", "assign_interview": "Assign Interview", "remove": "Remove", "join_meeting": "Join Meeting", "copy_link": "Copy Link", "employee_role_updated_success": "Employee role updated successfully", "failed_update_employee_role": "Failed to update employee role", "error_updating_employee_role": "Error updating employee role", "access_management": "Access", "role_action_success": "Role {actionType} successfully", "management": "Management", "user_roles": "User Roles", "user_permissions": "User Permissions", "search_user_role": "Search using user role", "add_new_role": "Add New Role", "no_roles_found": "No roles found. Add a new role to get started.", "no_roles_permission_found": "No roles-permissions found", "permission_counts": "Permission Counts", "last_modified": "Last Modified", "interview_details": "Interview Details", "edit": "Edit", "user_roles_alt": "User roles image", "assessment_link_copied_to_clipboard": "Assessment link copied to clipboard", "failed_to_copy_link": "Failed to copy link", "assessment_shared_successfully": "Assessment shared successfully", "failed_to_share_assessment": "Failed to share assessment", "an_error_occurred_while_sharing_assessment": "An error occurred while sharing assessment", "share_final_assessment": "Share Final Assessment", "you_can_share_the_final_assessment_link_to_the_candidate_for_them_to_take_the_test_remotely_they_won_t_be_able_to_see_the_correct_answers": "You can share the final assessment link to the candidate for them to take the test remotely. They won't be able to see the correct answers.", "assessment_link": "Assessment Link", "share_assessment": "Share Assessment", "sharing": "Sharing...", "add_role": "Add Role", "minutes": "minutes", "edit_role": "Edit Role", "delete_role": "Delete Role", "role_name": "Role Name", "role_name_required": "please enter role name", "role_name_placeholder": "Enter role name", "role_description": "Role Description", "role_description_placeholder": "Enter role description", "role_description_required": "Role description is required", "role_delete_confirmation": "Are you sure you want to delete this role?", "role_delete_warning": "This action cannot be undone. All users with this role will lose their associated permissions.", "submit": "Submit", "delete": "Delete", "saving": "Saving...", "deleting": "Deleting...", "role_added_success": "Role added successfully", "role_updated_success": "Role updated successfully", "role_deleted_success": "Role deleted successfully", "failed_add_role": "Failed to add role", "failed_update_user_role": "Failed to update role", "failed_delete_role": "Failed to delete role", "failed_update_role": "Failed to update role", "error_updating_role": "Error updating role", "edit_permissions_for": "Edit Permissions for", "cannot_schedule_interview_once_final_assessment_is_generated": "Cannot schedule interview once final assessment is generated", "loading_permissions": "Loading permissions...", "failed_load_permissions": "Failed to load permissions. Please try again.", "permissions_description": "Here, you can modify the access levels and permissions associated with this role to match current organizational needs or individual job responsibilities. Each permission is listed with a brief description to help you make informed decisions.", "role_permissions": "Role Permissions", "select_all": "Select All", "no_permissions_found": "No permissions found for this role.", "make_changes_to_enable_save": "Make changes to permissions to enable save", "at_least_one_permission": "At least one permission must be selected", "save_permissions": "Save Permissions", "permissions_updated_success": "Permissions updated successfully", "failed_update_permissions": "Failed to update permissions. Please try again.", "enter_role_name": "Enter role name", "role_name_req": "Please enter role name", "min_name": "Role name must be at least 2 characters", "department_name_req": "Please enter department name", "email_is_required": "Please enter email", "please_enter_a_valid_email": "Please enter a valid email", "please_select_valid_role": "Please select valid roles", "please_select_valid_department": "Please select valid departments", "first_name_req": "First name is required", "last_name_req": "Please enter last name", "email_req": "Email is required", "department_req": "Please select department", "role_req": "Please select role", "order_interview_req": "Please enter order of interview", "error_fetching_departments": "Error fetching departments. Please try again.", "question_added_successfully": "Question added successfully", "skill_not_found": "Skill not found", "an_unexpected_error_occurred_while_adding_the_question": "An unexpected error occurred while adding the question", "question_type": "Question Type", "multiple_choice_question": "Multiple Choice Question", "true_false": "True/False", "select_question_type": "Select question type", "options": "Options", "interview_": " Interview", "correct_answer": "Correct Answer", "adding_question": "Adding Question", "select_correct_answer": "Select correct answer", "enter_your_question": "Enter your question", "question_is_required": "Please enter question", "question_type_is_required": "Please select question type", "option_id_is_required": "Please enter option ID", "option_text_is_required": "Please enter option", "options_are_required": "Options are required", "at_least_2_options_are_required": "At least 2 options are required", "correct_answer_is_required": "Please select correct answer", "please_enter_valid_email": "Please enter valid email", "invalid_data": "Invalid data", "assessment_already_submitted": "Assessment already submitted", "max_career_based_questions_reached": "Max career based questions reached", "max_role_specific_questions_reached": "Max role specific questions reached", "max_culture_specific_questions_reached": "Max culture specific questions reached", "candidate_not_found": "Candidate not found", "job_not_found": "Job not found", "assessment_already_shared": "Assessment already shared", "assessment_already_exists": "Assessment already exists", "assessment_created": "Assessment created successfully", "assessment_not_found": "Assessment not found", "no_questions_found": "No questions found", "final_assessment_questions_created": "Final assessment questions created successfully", "assessment_questions_not_found": "Assessment questions not found", "assessment_questions_deleted": "Assessment questions deleted successfully", "failed_to_send_assessment_email": "Failed to send assessment email", "invalid_assessment_link": "Invalid assessment link", "invalid_question_ids": "Invalid questions", "assessment_status_fetched": "Assessment status fetched successfully", "email_mismatch": "Email mismatch", "unauthorized_access": "Unauthorized access", "generating_assessment_url": "Generating assessment URL...", "no_assessment_token_found_in_url": "No assessment token found in URL", "not_authenticated": "Oops! This link belongs to a different email address.", "employee_added": "Employee registered successfully", "employee_already_registered": "Employee already registered for your organization", "employee_already_registered_with_diff_org": "Employee registered with different organization", "failed_to_add_employee": "Failed to register employee", "cannot_add_employee_user_registered_with_another_org": "Employee already associated with another organization", "complete_assessment": "Complete Assessment", "next_skill_assessment": "Next Skill Assessment", "questions_answered": "Questions Answered", "can_not_add_new_skill_in_career_based_skill": "Can not add new skill in career based skill", "group": "Group", "skill": "Skill", "please_select_atleast_one_question": "Please select alteast one question", "skill_added_successfully": "<PERSON>ll added successfully", "share_assessment_link_to_candidate": "Share Assessment Link To Candidate", "failed_to_generate_assessment_link": "Failed to generate assessment link", "token_req_msg": "Failed to generate assessment link", "otp_expired": "otp expired please request a new otp", "email_exist": "email exist", "unsupported_email": "unsupported email", "success": "success", "otp_verified": "otp verified successfully", "submitted": "submitted successfully", "password_not_updated": "password not updated", "password_update_failed": "password update failed", "profile_updated": "profile updated", "user_not_exist": "user not exist", "forgot_user": "email entered is not associated with any registered account", "failed": "something went wrong", "user_session_deleted": "user session deleted", "cannot_update_role": "Administrator role cannot be update", "wrong_current_pass": "wrong current pass", "email_not_found": "email not found", "email_not_verified": "email not verified", "try_again": "Something wrong try again", "reset_password": "Reset password", "reset_password_error": "Reset password error", "wrong_otp": "Please enter a valid verification code", "presigned_url_generated": "Presigned url generated", "fetch_success": "Fetch success", "fetch_failed": "<PERSON><PERSON> failed", "invalidParams": "Invalid params", "job_fetch_success": "Job fetch success", "user_roles_fetch": "User roles fetch", "user_role_added": "User role added successfully", "user_role_updated": "User role updated successfully", "user_role_deleted": "User role deleted successfully", "role_already_exists": "Role name already exists", "role_not_found": "Role not found", "role_permissions_fetch": "Role permissions fetch", "role_permissions_updated": "Role permissions updated", "at_least_one_permission_required": "Please select at least one permission", "add_failed": "Add failed", "update_failed": "Update failed", "delete_failed": "Delete failed", "final_assessment_created": "Final assessment created successfully", "questions_fetched": "Questions fetched successfully", "departments_fetch": "Departments fetch", "department_already_exists": "Department already exists", "department_not_found": "Department not found", "department_has_employees": "Cannot delete department with active employees", "departments_by_organization_fetch": "Departments by organization fetch", "employees_fetch": "Employees fetch", "employee_role_updated": "Employee role updated successfully", "employee_deleted": "Employee deleted successfully", "employee_not_found": "Employee not found", "email_already_exists": "Email already exists", "department_id_required": "Select department ", "name_required": "Candidate name is required", "organization_id_required": "Organization id required", "no_employees_data": "No employees data", "no_update_data_provided": "No update data provided", "skills_generated": "Skills generated successfully", "skills_generation_failed": "Skills generation failed! Please try again.", "job_requirement_generated": "Job requirement generated successfully", "job_requirement_generation_failed": "Job requirement generation failed! Please try again.", "job_details_saved": "Job details saved successfully", "job_details_save_failed": "Job details save failed! Please try again.", "job_update_success": "Job updated successfully", "job_update_failed": "Job update failed! Please try again.", "pdf_generated_success": "PDF generated successfully in downloads folder", "pdf_generation_failed": "PDF generation failed! Please try again.", "pdf_only": "Only PDF files are allowed.", "pdf_size": "File size exceeds the maximum limit of 10 MB.", "pdf_select": "Please select a PDF file first.", "pdf_invalid": "Failed to process the PDF: Invalid response format", "pdf_extract": "Failed to extract data from PDF", "interviewer": "Interviewer", "candidate_resume_": "Candidate Resume", "pdf_error": "An error occurred while processing the PDF", "upload_assessment": "Upload Assessment", "experience_must_be_number": "Experience must be a number or decimal", "experience_min_value": "Experience must be greater than 0", "experience_max_value": "Experience must be 50 years or less", "experience_max_length": "Experience must be at most 4 characters long", "title_alpha_only": "Title can only contain letters, spaces, and common special characters (/, &, ,, ., ', (), +, #, -, :)", "state_alpha_only": "State can only contain letters, spaces and commas", "city_alpha_only": "City can only contain letters, spaces and commas", "common": {"home": "Home", "hm_dashboard": "HM Dashboard", "job_requirement_generations": "Job Requirement Generations", "notifications": "Notifications", "clear_all": "Clear All", "no_more_notifications": "No more notifications", "no_notifications_found": "No notifications found", "data_security_msg": "We prioritize your data’s security. With encryption at every step, your privacy is secure and protected."}, "dashboard": {"hiring_manager_dashboard": "Hiring Manager Dashboard", "jobs_created": "Jobs Created", "active_jobs": "Active Jobs", "upcoming_interviews": "Upcoming Interviews", "candidates_hired": "Candidates <PERSON><PERSON>", "resumes_on_hold": "Resumes on Hold", "scheduled_interviews": "Scheduled Interviews", "resume_on_hold": "Resume On-Hold", "create": "Create", "set_view_questionarie": "Set/View Questionnaire", "view_your_summary": "View Your Summary", "a_new_job": "A New Job", "screen_resumes": "Screen Resumes", "conduct": "Conduct", "interviews": "Interviews", "screen": "Screen", "resumes": "Resumes", "past_interviews": "Past Interviews", "search_placeholder": "Search using name, user role, department etc", "candidate_resume": "Candidate Resume", "no_interviews_found": "No interviews found"}, "header": {"logout": "Logout", "session_expired": "Session Expired", "job_requirement_generations": "Job Requirement Generations", "resume_screening": "Resume Screening", "conduct_interview": "Conduct Interview", "settings": "Settings", "my_profile": "My Profile"}, "jobRequirement": {"select_hiring_type": "Select Hiring Type", "hiring_type": "Hiring Type", "select_the_type_of_hiring_below_to_proceed_with_the_job_description_creation": "Select the type of hiring below to proceed with the job description creation.", "internal_hiring": "Internal Hiring", "external_hiring": "External Hiring", "craft_job_descriptions_for_internal_openings_and_promote_opportunities_within_your_organization": "Craft job descriptions for internal openings and promote opportunities within your organization.", "craft_job_descriptions_for_external_openings_and_promote_opportunities_within_your_organization": "Craft job descriptions for external openings and promote opportunities within your organization.", "attract_top_talent_by_creating_compelling_job_descriptions_for_external_candidates": "Attract top talent by creating compelling job descriptions for external candidates.", "continue": "Continue", "failed_to_update_job_description": "Failed to update job description", "update_job_description_error": "Failed to update job description. Please try again.", "have_job_discription": "Have an existing job description document?", "extracted_data": "We've extracted details from your uploaded job description to fill the form. Fields highlighted below require additional input as the data wasn't found in the document.", "basic_details": "1. Basic Details", "job_title": "Job Title", "eneter_job_title": "Enter Job Title", "employment_type": "Employment Type", "select_employment_type": "Select Employment Type", "depeartment": " Department", "salary_range": "Salary Range", "enter_salary_range": "e.g. 50,000 - 70,000 ($ Will be added automatically)", "salsry_cycle": "Salary Cycle", "select_salary_cycle": "Select Salary Cycle", "job_location": "Job Location Type", "select_job_location": "Select Job Location", "select_location_type": "Select location Type", "state": "State", "enter_state": "Enter State", "city": "City", "enter_city": "Enter City", "role_overview": "2. Role Overview", "overview": "Overview", "overview_placeholder": "Tell us about this position in one or two sentences. (What's the purpose of this role? What Impact will It have on your team or organization?)", "experince_level_heading": "3. Experience Level", "experince_level": "Experience Level", "select_experience_level": "Select Experience Level", "key_responsibilities_heading": "4. Key Responsibilities", "key_responsibilities": "Key Responsibilities", "key_responsibilities_placeholder": "List the main tasks and responsibilities for this role. (Pro tip: Start with action verbs like Manage,Coordinate,Ensure,Track etc.)", "required_skills_heading": "5. Required Skills & Qualifications", "educational_requirments": "Educational Requirements", "educational_requirments_placeholder": "(e.g., Bachelor's Degree in Business Administration, High School Diploma, etc.)", "certifications": "Certifications", "optional": "(Optional)", "certifications_placeholder": "(e.g., OSHA Certification, PMP, Six Sigma)", "specfic_skills_or_software_knowledge": "Specific Skills or Software Knowledge", "specfic_skills_or_software_knowledge_placeholder": "(e.g., Microsoft Office, ERP Systems, Time Management, etc,)", "years_of_experince": " Years of Experience Needed", "years_of_experince_placeholder": "(e.g., 3 or 3.5)", "ideal_traits_heading": " 6. Ideal Candidate Traits", "ideal_traits": "Ideal Candidate Traits", "ideal_traits_placeholder": "Describe the qualities you're looking for in the ideal candidate (e.g., Detail-oriented, proactive, excellent communicator, team player)", "about_company_heading": "7. About the Company", "about_company": "About the Company", "about_company_placeholder": "Provide a brief description of your company, its mission, and values. (e.g., Company culture, work environment, etc.)", "job_benifits_heading": "8. Perks & Benefits ", "job_benifits": "Job Benefits", "job_benifits_placeholder": "Let us know what perks and benefits you offer to make this job attractive: (e.g. Flexible schedule, healthcare benefits, 401 (k), professional development opportunities)", "tone_and_style_heading": "9. <PERSON><PERSON> and <PERSON>", "tone_and_style": "Tone and Style", "tone_and_style_placeholder": "Select Tone & Style", "additional_information_heading": "10. Additional Information", "additional_information": "Additional Information", "additional_information_placeholder": "Any other information you want to include in the job description? (e.g., travel requirements, remote work options, etc.)", "additional_info_heading": "10. Additional Info ", "additional_info_placeholder": "Is there anything specific you'd like included in the job post or description?", "compliance_statement_heading": "11. Compliance Statement", "select_compliance_statement": "Choose Compliance Statements", "learn_more": "Learn More", "compliance_statement_placeholder": "Select compliance statements", "compliance_statement_part1": "By using these compliance statements, you acknowledge that Stratum 9 is not responsible for their accuracy or legal compliance and", "compliance_statement_part2": "waive any liability against the platform. ", "no_job_requirment_generated": "No job requirement generated yet. Please generate job requirement first.", "job_requirment_generation": "Job Requirement Generations", "edit_job_description": "Edit Job Description for", "job_requirment_for": "Job Requirement for", "top_performance_based_skills": "Top Performance-based Skills Required In ", "generate_job_requirement": "Generate Job Requirement"}, "apiResponse": {"dashboard_counts_fetch_success": "Dashboard counts fetched successfully", "dashboard_counts_fetch_failed": "Dashboard counts fetch failed", "no_pdf_file_uploaded": "No PDF file uploaded", "internal_server_error": "Internal server error", "job_description_processed_successfully": "Job description processed successfully", "failed_to_process_the_pdf_file_ensure_it_is_a_valid_pdf": "Failed to process the PDF file. Please ensure it's a valid PDF.", "failed_to_extract_form_fields": "Failed to extract form fields", "job_description_processed_failed": "Job description processed failed", "job_not_found": "Job not found", "failed_to_update_job": "Failed to update job", "jobs_meta_fetch_success": "Jobs metadata fetched successfully", "failed_to_fetch_jobs_meta": "Failed to fetch jobs metadata", "open_ai_key_not_configured": "OpenAI API key not configured", "failed_to_initialize_openai_client": "Failed to initialize OpenAI client", "open_ai_client_not_initilezed": "OpenAI client not initialized"}, "careerBasedSkills": {"skill_name_required": "Skill name is required", "skill_description_required": "Description is required", "skill_name_length": "Skill name must be at least 3 characters long", "skill_description_length": "Description must be at least 3 characters long", "skill_description_max_length": "Skill description must be at most 200 characters", "skill_name_not_numeric": "Skill name cannot be numeric", "skill_description_not_numeric": "Skill description cannot be numeric", "skill_name_exists": "Skill name already exists", "skill_name_max_length": "Skill name must be at most 50 characters long"}, "view_all_candidates": "View All Candidates", "screen_resume_manually": "Screen Resume Manually", "view_applicarion_source": "View Application Source", "archive_job": "Archive ", "edit_job": "Edit", "no_jobs_found": "No jobs found", "add_new_job": "Add New Job", "candidate_name": "Candidate Name", "date_submitted": "Date Submitted", "source": "Source", "archived_on": "Archived On", "reason_for_archiving": "Reason for Archiving", "analyze_candidate_resume": "Analyze Candidate Resume", "no_more_candidates_to_fetch": "No more candidates", "no_candidates_found": "No candidates found", "no_applications_found": "No applications found", "back_to_jobs": "Back to Jobs", "back_to_start": "Back to Start", "save_and_next": "Save and Next", "no_role_specific_skills_found": "No role-specific skills data available. Please generate job skills first.", "no_desc_available": "No description available", "top": "Top", "hm_dashboard": "Hiring Manager Dashboard", "performance_skills_part1": "Based on the job details you provided and the finalized description, we’ve identified the Career-based Skills most critical to success.", "performance_skills_part2": "These Career-based skills are essential for basic performance in the role", "performance_skills_part3": "You can edit or adjust the selected skills at any time.", "edit_skills": "Edit skills", "add_change_performance_based_skills": "Add or Change Performance-based Skills", "double_click_to_edit": "Double click on any skill card to see its definition, as it pertains to high performance.", "note": "Note: ", "your_selection": "Your Selection", "already_selected": "Already Selected", "available_for_selection": "Available for Selection", "role_specific_skills": "Role-Specific Skills", "culture_specific_skills": "Culture-Specific Skills", "no_culture_specific_skills_found": "No culture-specific skills data available. Please generate job skills first.", "carrer_based_skills": "Career-Based Skills", "discription": "Description", "skill_name": "Skill Name", "save_cahnges": "Save Changes", "no_carrer_skills_available": "No career skills data available. Please generate job skills first.", "resume_screening": "Resume Screening", "application_for": "Application for", "active_jobs": "Active Jobs", "no_active_jobs_found": "No active jobs found", "approved_by_s9": "Approved By S-9 Inner View", "rejected_by_s9": "Rejected By S-9 Inner View", "reasons_for_good_match": "Reasons why they are a good match:", "approved_candidates": "Approve Candidate", "rejected_candidates": "Reject Candidate", "back_to_screening": "Back To Screening", "candidates_for": "Candidates For ", "top_ten_candidates": "Top Candidates", "based_on_interview_date": "Based on interviews to date.", "lined_up_for": " Lined-Up For", "candidates_analysis": "Candidates Analysis", "other_candidates": "Other Candidates", "rancked_by_resume": "Ranked by Resume/Job Description Analysis", "add_candidates_info": "Add Candidates Info", "promote_candidate": "Promote Candidate", "demote_candidate": "Demote Candidate", "manual_upload_resume": "Manual Upload Resume For", "view_pending_action": "View Pending Actions", "current": "Current", "completed": "Completed", "behavioral": "Behavioral", "performance": "Performance", "additional": "Additional", "questions": "Questions", "role_specific_interview": "Role-Specific Interview", "culture_specific_interview": "Culture-Specific Interview", "career_based_skills_and_general_interview": "Career-Based Skills (Hard Skills) and General Interview", "gender": "Gender", "select_gender": "Please select gender", "upload_resume": "Upload Resume", "upload_assesment": "Upload Assessment", "additional_info_": "Additional Information", "additional_info_desc": "Describe Candidate’s Answer & Other Observations You’ve Made", "your_notes": "Your Notes", "add_candidates_resume": "+ Add Another Candidate's Resume", "analyze": " Analyze", "candidate_answer": "Candidate Answer", "reset": "Reset", "manual_resume_upload_for": "Manual resume upload for", "operational_admin": "Operational Admin", "additional_information": "Enter additional information*", "candidates": "Candidates", "enter_additional_info_about_candidate": "Add any remarks, context, or observations about the candidate (Optional)", "confirm_delete_role": "Are you sure you want to delete this role?", "role_has_employees": "Employees are currently assigned to this role. Please remove them before deletion.", "update_interview_order": "Update Interview Order", "updating_order_for": "Updating order for", "enter_interview_order": "Enter interview order", "update_order": "Update Order", "update_interview_order_success": "Interview order updated successfully", "update_interview_order_error": "Failed to update interview order", "default_department_cannot_be_deleted": "Default department cannot be deleted", "add_new_department": "Add New Department", "no_departments_found": "No departments found. Add a new department to get started.", "no_department_search_results": "No department found relevant to your query", "no_role_search_results": "No role found relevant to your query", "same_as_current_order": "Same as current order", "employee_interview_order_updated": "Employee interview order updated successfully", "failed_update_interview_order": "Failed to update interview order", "invalid_sort_order": "Invalid sort order", "some_error_occurred": "Some error occurred", "generate_final_candidate_assessment": "Generate Final Candidate Assessment", "before_proceeding_please_review_the_candidates_overall_performance": "Before proceeding, please review the candidate's overall performance from the interview rounds, skills evaluation, and behavioral feedback. The final assessment will summarize the candidate's qualifications and overall fit for the role.", "final_assessment_warning_message": "Once the final assessment is created for this candidate, they will no longer be eligible to participate in any additional interview rounds. If any interview round remains incomplete, please ensure that it is conducted before proceeding with the final assessment for this candidate.", "information_preview": "Information Preview", "interview_round_scores": "Interview Round Scores", "skills_evaluation": "Skills Evaluation", "behavioral_feedback": "Behavioral Feedback", "candidate_fit": "Candidate Fit", "ats_score": "ATS Score", "cultural_fit": "Cultural Fit", "qualifications": "Qualifications", "key_strengths_and_weaknesses": "Key Strengths and Weaknesses", "from_previous_rounds": "From Previous Rounds", "generate_final_assessment": "Generate Final Assessment", "back": "Back", "creating_final_assessment": "Creating Final Assessment", "title_required": "Job title is required", "title_min": "Title must be at least 3 characters", "title_max": "Title must be at most 150 characters", "employment_type_required": "Select employment type", "salary_range_required": "Salary range is required", "salary_range_format": "Salary range must be in the format $50,000 - $70,000", "salary_cycle_required": "Select salary cycle", "location_type_required": "Select location type", "state_required": "State is required", "state_min": "State must be at least 3 characters", "state_max": "State must be at most 50 characters", "city_required": "City is required", "city_min": "City must be at least 3 characters", "city_max": "City must be at most 50 characters", "role_overview_required": "Overview is required", "role_overview_min": "Role overview must be at least 3 characters", "role_overview_max": "Role overview must be at most 1500 characters", "experience_level_required": "Select experience level", "responsibilities_required": "Responsibilities are required", "responsibilities_min": "Responsibilities must be at least 3 characters", "responsibilities_max": "Responsibilities must be at most 1500 characters", "education_required": "Education requirements are required", "education_min": "Education requirements must be at least 3 characters", "education_max": "Education requirements must be at most 1500 characters", "certifications_max": "Certifications must be at most 1500 characters", "skills_required": "Software knowledge or skills is required", "skills_min": "Skills and software expertise must be at least 3 characters", "skills_max": "Skills and software expertise must be at most 1500 characters", "experience_required_required": "Experience is required", "experience_required_min": "Experience required must be at least 3 characters", "experience_required_max": "Experience required must be at most 150 characters", "traits_required": "Candidate traits are required", "traits_min": "Ideal candidate traits must be at least 3 characters", "traits_max": "Ideal candidate traits must be at most 1500 characters", "company_required": "Company description is required", "company_min": "About company must be at least 3 characters", "company_max": "About company must be at most 1500 characters", "perks_max": "Perks and benefits must be at most 1500 characters", "tone_required": "Select tone and style", "additional_max": "Additional info must be at most 200 characters", "compliance_required": "Compliance statements are required", "compliance_min": "Please select at least one compliance statement", "compliance_type_error": "Please select at least one compliance statement", "show_compliance_required": "You must agree to the compliance statement disclaimer", "resume_required": "Upload resume is required", "resume_type": "Resume must be a PDF", "resume_size": "Resume size must be less than 5MB", "unsupported_file_type": "The selected file type is not supported. Please upload a PDF file.", "assessment_type": "Assessment must be a PDF", "assessment_size": "Assessment size must be less than 5MB", "name_min": "Name must be at least 3 characters", "email_required": "Email is required", "email_valid": "Candidate email must be a valid email", "gender_required": "Gender is required", "gender_valid": "Please select a valid gender", "at_least_one_candidate": "At least one candidate is required", "candidate_joined": "<PERSON><PERSON><PERSON> joined", "initializing_your_video": "Initializing your video...", "waiting_for_candidate_to_join": "Waiting for candidate to join...", "cam": "Cam", "mic": "Mic", "leave": "Leave", "interview_doesnt_exist": "Interview doesn't exist", "join_your_online_interview": "Join Your Online Interview", "you_re_about_to_start_your_online_interview": "You’re about to start your online interview. Please enter your email to confirm your identity and proceed.", "invalid_invitation_link": "Invalid invitation link", "link_copied": "Link copied to clipboard", "recording_started": "Recording started", "no_active_job_found": "No active job found.", "somthing_went_wrong": "Something went wrong", "additional_info": "Additional Info", "resume_analysis": "Resume Analysis For", "cannot_update_default_department": "Administrator department cannot be update", "job_application_not_found": "Job application not found", "password": "Password", "forgot_password": "Forgot Password?", "login": "<PERSON><PERSON>", "enter_your_email": "Enter your email", "enter_your_password": "Enter your password", "send_verification_code": "Send Verification Code", "back_to_login": "Back To Login", "back_to": "Back to", "hello": "Hello", "welcome_back": "Welcome", "forgot": "Forgot", "enter_verification_code": "Enter Verification Code", "verify": "Verify", "resend_verification_code": "Resend Verification Code", "code": "Code", "otp_verified_successfully": "Verification code verified successfully", "resend_access_code_in": "Resend verification code in", "seconds": "seconds", "new_password": "New Password", "confirm_password": "Confirm Password", "reset_password_success": "Your password has been reset successfully.", "reset_password_failed": "Failed to reset password. Please check your OTP or try again.", "new_password_required": "New password is required", "new_password_min": "New password must be at least 8 characters", "new_password_max": "New password must not exceed 20 characters", "confirm_password_required": "Confirm password is required", "confirm_password_mismatch": "Passwords do not match", "password_placeholder": "Enter new password", "confirm_password_placeholder": "Confirm new password", "reset_heading": "Reset", "password_heading": "Password", "archive": "Archive", "restore": "Rest<PERSON>", "restoring": "Restoring...", "you_dont_have_permission_to_view_archive_content": "You don't have permission to view archive content", "jobs": "Jobs", "pdf_parsing_failed": "Error in parsing PDF file", "job_requirements_generated": "Job requirements generated successfully", "profile_updated_successfully": "Profile updated successfully", "failed_to_update_profile": "Failed to update profile", "an_error_occurred_while_updating_profile": "An error occurred while updating profile", "edit_profile": "Edit Profile", "change_your_profile_details": "Change your profile details.", "upload_picture": "Upload Picture", "save_changes": "Save Changes", "saving_changes": "Saving Changes", "organization_name": "Organization Name", "change_profile_picture": "Change Profile Picture", "error_uploading_image": "Error uploading image", "failed_to_upload_image": "Failed to upload image", "error_updating_profile": "Error updating profile", "last_name_required": "Please enter last name", "last_name_min": "Last name must be at least 3 characters", "last_name_max": "Last name must be at most 50 characters", "first_name_required": "Please enter first name", "first_name_min": "First name must be at least 3 characters", "first_name_max": "First name must be at most 50 characters", "organization_code": "Organization Code", "account_created_on": "Account Created On", "failed_to_load_profile": "Failed to load profile", "update": "Update", "picture": "Picture", "my_interviews": "My Interviews", "employee": "Employee", "no_employees": "No employees in this department", "failed_role_operation": "Something went wrong. Please try again.", "min_role_name": "Role name must be at least 2 characters", "max_role_name": "Role name cannot exceed 50 characters", "max_department_name": "Department name cannot exceed 50 characters", "min_department_name": "Department name must be at least 2 characters", "max_last_name": "Last name must be at most 50 characters", "max_first_name": "First name must be at most 50 characters", "min_last_name": "Last name must be at least 3 characters", "min_first_name": "First name must be at least 3 characters", "you_have_seen_all_records": "You have seen all records", "you_have_seen_all_records_search": "You have seen all records relevant to your query", "name_max": "Candidate name must be at most 50 characters", "email_max": "Candidate email must be at most 50 characters", "pdf_name": "File name must be at most 50 characters", "pdf_name_special_chars": "File name cannot contain special characters. Only letters, numbers, spaces, hyphens, and underscores are allowed", "no_more_jobs_found": "No more active jobs found", "add_candidate": "Add Candidate", "failed_to_load_transactions": "Failed to load transactions", "error_fetching_transactions": "Error fetching transactions", "failed_to_fetch_subscription_data": "Failed to fetch subscription data", "error_fetching_subscription": "Error fetching subscription", "subscription_canceled_success": "Subscription canceled successfully", "failed_to_cancel_subscription": "Failed to cancel subscription", "error_cancelling_subscription": "Error cancelling subscription", "subscription_activated_success": "Subscription activated successfully", "error_retrieving_subscription": "Could not retrieve subscription details. Please contact support.", "subscription_thank_you": "Thank you for subscribing to our service!", "navigating_to_dashboard": "Navigating to dashboard...", "viewing_subscription_details": "Viewing your subscription details...", "additional_info_submission": "Additional Info Submission", "before_hiring": "Before Hiring", "additional_info_description": "You can provide additional information about the candidate below so that S9-Innerview can give a final verdict about the candidate being a right fit.", "view_candidate_info": "View Candidate Info", "additional_info_placeholder": "Enter additional information about the candidate", "additional_info_max_1000_chars": "Additional info must be at most 1000 characters long", "additional_info_min_10_chars": "Additional info must be at least 10 characters long", "candidates_uploaded_successfully": "Candidates uploaded successfully!", "promoted": "Promoted", "demoted": "Demoted", "additional_info_submitted_successfully": "Additional info submitted successfully", "upload_document": "Upload Document", "additional_info_required": "Additional info is required", "please_upload_a_document": "Please upload a document", "update_application_status_success": "Application status updated successfully", "update_application_status_failed": "Failed to update application status", "top_candidates_retrieved": "Top candidates retrieved successfully", "get_top_candidates_failed": "Failed to Get top candidates", "candidate_application_not_found": "Candidate application not found", "update_rank_status_failed": "Failed to update rank status", "update_rank_status_success": "Successfully updated rank status", "fetch_candidate_details_failed": "Failed to fetch candidate details", "candidate_not_found_for_org": "Candidate not found", "additional_info_saved": "Additional info saved successfully", "save_additional_info_failed": "Failed to save additional info", "unknown_error": "Unknown error occurred", "candidates_fetched": "Candidates fetched successfully", "get_all_candidates_failed": "Failed to get all candidates", "get_interview_history_failed": "Failed to get interview history", "profile": "Profile", "post_applied_for": "Post Applied For", "current_round": "Current Round", "resume_approved_by": "Resume Approved By", "hire": "<PERSON>re", "approve": "Approve", "reject": "Reject", "skill_specific_assessment": "Skill-Specific Assessment", "interview_history": "Interview History", "ai_summary": "AI Summary", "skills_score": "Skills Score", "development_recommendations": "Development Recommendations", "skills_summary": "Skills Summary", "improvement_areas": "Improvement Areas", "round": "Round", "summary": "Summary", "interview_by": "Interviewed by", "scores": "Scores", "highlights": "Highlights", "your_performance_feedback": "Your Performance Feedback", "hard_skills": "Hard Skills", "interviewer_avatar": "Interviewer <PERSON><PERSON>", "somthing_wrong": "Something went wrong", "updated_application_status_success": "Candidate status updated successfully", "additional_info_max_200_chars": "Additional info must be at most 200 characters long", "pdf_size_five_mb": "File size exceeds the maximum limit of 5 MB.", "invalid_or_inactive_plan_id": "Invalid or inactive plan ID", "invalid_pricing_id_for_the_given_plan": "Invalid pricing ID for the given plan", "no_stripe_customer_found_for_the_organization": "No Stripe customer found for the organization", "stripe_customer_not_found_or_has_been_deleted": "Stripe customer not found or has been deleted", "error_retrieving_stripe_customer": "Error retrieving Stripe customer", "no_active_subscription_found_for_this_organization": "No active subscription found for this organization", "subscription_plan_or_pricing_details_not_found": "Subscription plan or pricing details not found", "current_subscription_retrieved_successfully": "Current subscription retrieved successfully", "error_retrieving_current_subscription": "Error retrieving current subscription", "subscription_will_be_expired_at_the_end_of_the_current_billing_cycle": "Subscription will be expired at the end of the current billing cycle", "pricing_not_found": "Pricing not found", "checkout_session_created_successfully": "Checkout session created successfully", "error_creating_checkout_session": "Error creating checkout session", "customer_already_exists": "Customer already exists", "stripe_customer_created_successfully": "Stripe customer created successfully", "error_creating_stripe_customer": "Error creating Stripe customer", "all_available_plans_retrieved_successfully": "All available plans retrieved successfully", "error_retrieving_subscription_plans": "Error retrieving subscription plans", "webhook_processed_successfully": "Webhook processed successfully", "error_processing_webhook": "Error processing webhook", "organization_not_found": "Organization not found", "you_have_already_selected_this_plan_please_choose_a_different_plan_to_proceed": "You have already purchased this plan, please choose a different plan to proceed", "using_free_plan_benefit_quota": "Using free plan benefit quota", "benefit_quota_available": "Benefit quota available", "invalid_benefit_type": "Invalid benefit type", "error_checking_subscription_benefit": "Error checking subscription benefit", "no_subscription_plan_found_and_no_free_plan_available": "No subscription plan found and no free plan available", "no_subscription_found_and_error_retrieving_free_plan": "No subscription found and error retrieving free plan", "job_posting_limit_reached": "You’ve reached your job posting limit. Upgrade your plan to post more.", "benefit_job_postings_not_found_in_free_plan": "Job postings benefit not found in free plan", "free_plan_does_not_include_job_postings_quota": "Free plan does not include job postings quota", "resume_screening_limit_reached": "Resume screening limit reached. Please upgrade your plan to continue.", "benefit_resume_screening_not_found_in_free_plan": "Resume screening benefit not found in free plan", "free_plan_does_not_include_resume_screening_quota": "Free plan does not include resume screening quota", "you_can_only_screen_1_resume_with_your_current_plan": "You can only screening 1 resume with your current plan", "you_have_reached_your_manual_resume_upload_limit_please_upgrade_your_plan": "You have reached your manual resume upload limit, please upgrade your plan", "benefit_manual_resume_upload_not_found_in_free_plan": "Manual resume upload benefit not found in free plan", "free_plan_does_not_include_manual_resume_upload_quota": "Free plan does not include manual resume upload quota", "you_can_only_upload_1_resume_with_your_current_plan": "Only 1 candidate resume upload allowed. Please upgrade for more.", "roles_and_permissions": "Roles and Permissions", "no_more_roles_to_fetch": "No more roles", "no_more_employees_to_fetch": "No more employees", "status": "Status", "active": "Active", "inactive": "Inactive", "employee_status_updated": "Employee status updated successfully", "confirm_delete_employee": "Are you sure you want to delete \"{name}\" employee with role - \"{role}\"?", "employee_delete_success": "Employee {name} has been successfully deleted", "cannot_delete_employee_with_upcoming_interviews": "Cannot inactive employee as they have upcoming interviews scheduled", "cannot_delete_employee_with_active_job_applications": "Cannot delete employee as they are managing active job applications", "cannot_delete_employee_with_active_jobs": "Cannot delete employee as they are managing active jobs", "failed_employee_delete_operation": "Failed to delete employee", "cannot_delete_employee_with_job_application_status_changes": "Cannot delete employee who has changed job application status", "select_interview_order": "Select Interview Order", "you_dont_have_permission_to_schedule_interview": "You don't have permission to schedule interview", "stripeSubscriptionId_not_found": "Stripe subscription ID not found", "resume_upload_failed": "Resume file upload failed - please try again", "resume_file_required": "Resume file is required", "assessment_file_upload_failed": "Assessment file upload failed - please try again", "failed_to_upload_files": "Failed to upload files", "candidates_skipped_success": "candidates skipped (already successful)", "candidate_skipped_success": "candidate skipped (already successful)", "processed_successfully": "processed successfully", "candidates_small": "candidates", "candidate_small": "candidate", "candidates_failed_to_process": "candidates failed to process", "candidate_failed_to_process": "candidate failed to process", "candidates_failed_to_upload_files": "candidates failed to upload files", "candidate_failed_to_upload_files": "candidate failed to upload files", "candidates_already_processed": "candidates have already been successfully processed", "candidate_already_processed": "candidate has already been successfully processed", "no_candidates_processed": "No candidates were processed", "all": "All", "candidate_upload_succ": "Candidate successfully uploaded", "enter_candidate_name": "Please enter full name of the candidate", "please_enter_candidate_email": "Please enter candidate’s email address", "max_5_candidates": "Maximum 5 candidates allowed", "email_duplicate_in_form": "Duplicate email found in the form", "top_performance_based_skills": "Top Performance-Based Skills Required In ", "performance_based_skills": "Performance-Based Skills", "no_interview_history_found": "No interview history found", "no_improvement_areas_data_found": "No improvement areas data found", "final_summary_generated_successfully": "Final summary generated successfully", "failed_to_generate_final_summary": "Failed to generate final summary", "generate_final_summary": "Generate Final Summary", "are_you_sure_you_want_to_create_final_assessment": "Are you sure you want to create a final assessment for this candidate?", "are_you_sure_you_want_to_generate_final_summary": "Are you sure you want to generate the final summary for this candidate?", "are_you_sure_you_want_to_hire_this_candidate": "Are you sure you want to hire this candidate?", "are_you_sure_you_want_to_reject_this_candidate": "Are you sure you want to reject this candidate?", "hire_candidate": "<PERSON><PERSON> Candidate", "reject_candidate": "Reject Candidate", "create": "Create", "generate": "Generate", "failed_to_get_assessment_status": "Failed to get assessment status", "interview_end_date": "Interview End Date", "login_failed": "<PERSON><PERSON> failed", "failed_to_fetch_checkout_url": "Failed to fetch checkout URL. Please try again.", "confirm": "Confirm", "confirm_status_change_title": "Confirm Status Change", "confirm_status_change_message": "Are you sure you want to change the status ?", "activity_logs": "Activity Logs", "no_activity_logs_found": "No activity logs found", "log_type": "Log Type", "comment": "Comment", "entity_type": "Entity Type", "activity_logs_fetched": "Activity logs fetched successfully", "activity_logs_fetch_failed": "Failed to fetch activity logs", "role_already_assigned": "This Role is already assigned to this employee", "action_by": "Action By", "search_using_name": "Search by name", "old_value": "Old Value", "new_value": "New Value", "plan_ends_on": "Plan ends on:", "subscription_canceled_successfully": "Subscription canceled successfully", "plan_expires_on": "Plan expires on:", "expires_on": "Expires on:", "next_billing_cycle": "Next billing cycle:", "no_skill_score_data_found": "No skill score data found", "generate_final_summary_failed": "Failed to generate final summary", "no_interview_found_to_update_status": "No interview found to update status", "interviews_not_completed": "Candidate's Interviews are not completed yet", "interview_feedback_pending": "Candidate's Interview feedbacks are pending", "final_assessment_not_exists": "Final assessment not exists", "no_final_assessment_data_found": "No final assessment data found", "no_final_summary_data_found": "No final summary data found", "final_summary_already_generated": "Final summary already generated", "cannot_update_feedback_before_ending_interview": "Cannot update feedback before ending interview", "history": "History", "cannot_schedule_interview_in_past": "Cannot schedule interview in past", "questions_downloaded_successfully": "Questions downloaded successfully", "disconnected": "Disconnected", "minimum_one_interview_round_required": "Minimum one interview round should be completed to generate final summary", "previous_value": "Previous Value", "updated_value": "Updated Value", "error_fetching_hired_candidates": "An error occurred while fetching hired candidates", "failed_to_fetch_hired_candidates": "Failed to fetch hired candidates", "on_hold": "On Hold", "interview_title": "Interview Title", "application_final_summary_failed": "Failed to get candidate final summary", "can_promote_or_demote_only_after_resume_screening": "Please analyze the candidate before promoting or demoting.", "can_promote_only_approved_candidates": "Can promote only approved candidates", "interview_feedback_not_found": "Interview feedback not found", "script_copied": "<PERSON><PERSON>t copied successfully", "failed_to_generate_script": "Failed to generate script", "error_generating_script": "Error in generating script", "no_data_found": "No data found", "interviewer_performance_feedback": "Interviewer Performance Feedback", "failed_to_delete_notifications": "Failed to delete notifications, please try again.", "enter_job_requirement": "Please add the job requirements.", "save": "Save", "generating_questions": "Generating Questions...", "error_updating_status": "Error while updating status, please try again.", "LocaleSwitcher": {"switchLocale": "Zu {locale, select, de {<PERSON><PERSON><PERSON>} en {Englisch} other {Unbekannt}} wechseln"}, "no_more_activity_logs_to_fetch": "No more activity logs available", "final_assessment_generated_failed": "Failed to generate final assessment", "invalid_first_name": "Enter valid first name", "invalid_last_name": "Invalid last name format", "invalid_org_code": "Organization Code must be 6-10 alphanumeric characters without spaces", "job_description_lacks_sufficient_content": "Job description lacks sufficient content for skills extraction", "are_you_sure_want_to_archive": "Are you sure you want to archive this job?", "job_archive": "Job Archive", "candidate_restored_successfully": "Candidate restored successfully", "failed_to_restore_candidate": "Failed to restore candidate", "job_archive_successfully": "Job archived successfully", "failed_to_job_archive": "Failed to archive job", "restore_candidate_successfully": "Candidate restored successfully", "candidate_archive_successfully": "Candidate archived successfully", "failed_to_archive_candidate": "Failed to archive candidate", "interview_order_must_be_number": "Interview order must be number", "invalid_department_name": "Please enter a valid department name", "questions_are_being_generated": "Interview questions are being generated. Please wait a moment and try again.", "no_interviewers_found": "No interviewers found", "job_restored_successfully": "Job restored successfully", "attachment": "Attachment", "interview_already_ended": "Interview already ended", "candidate_email": "Candidate <PERSON><PERSON>", "resume_not_available": "Resume not available", "min_first_name_length": "First name must be at least 3 characters long", "max_first_name_length": "First name must be at most 50 characters long", "min_last_name_length": "Last name must be at least 3 characters long", "max_last_name_length": "Last name must be at most 50 characters long", "invalid_location_name": "Invalid location. It can contain only letters"}