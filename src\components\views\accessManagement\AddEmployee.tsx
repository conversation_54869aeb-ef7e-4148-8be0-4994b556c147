"use client";
import React, { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import styles from "../../../styles/accessManagement.module.scss";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import { useForm, useFieldArray } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import Select from "@/components/formElements/Select";
import Button from "@/components/formElements/Button";
import { useRouter } from "next/navigation";
import { EmployeeForm, EmployeeStatus } from "@/interfaces/employeeInterface";
import { addEmployees } from "@/services/employeeService";
import { FaCheckCircle, FaTimesCircle } from "react-icons/fa";
import { employeesValidationSchema } from "@/utils/validationSchema";
import { FindDepartmentResponse, findDepartments } from "@/services/departmentService";
import { toastMessageError, normalizeSpaces } from "@/utils/helper";
import { findRole } from "@/services/roleService";
import Loader from "@/components/loader/Loader";
import ROUTES from "@/constants/routes";
import DeleteIcon from "@/components/svgComponents/DeleteIcon";
import { useTranslate } from "@/utils/translationUtils";

interface ICommonData {
  id: number;
  name: string;
}
const AddEmployee = () => {
  const router = useRouter();
  const t = useTranslations();
  const translate = useTranslate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [roles, setRoles] = useState<ICommonData[]>([]);
  const [departments, setDepartments] = useState<FindDepartmentResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [employeeStatuses, setEmployeeStatuses] = useState<EmployeeStatus[]>([]);
  const [departmentData, setDepartmentData] = useState<ICommonData>();
  const [showResults, setShowResults] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    defaultValues: {
      employees: [{ firstName: "", lastName: "", email: "", department: 0, role: 0 }],
    },
    resolver: yupResolver(employeesValidationSchema(translate)),
    mode: "onChange",
  });

  useEffect(() => {
    // Get the URL search parameters
    const searchParams = new URLSearchParams(window.location.search);
    const id = searchParams.get("departmentId");
    const name = searchParams.get("departmentName");

    if (id && name) {
      setDepartmentData({ id: +id, name: name });
      setValue("employees.0.department", +id);
    }
  }, []);

  // Fetch roles and departments when component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setLoadError(null);

        // Fetch roles and departments in parallel
        const [rolesResponse, departmentsResponse] = await Promise.all([findRole(), findDepartments()]);

        if (rolesResponse.data?.success && Array.isArray(rolesResponse.data.data)) {
          setRoles(rolesResponse.data.data);
        } else {
          toastMessageError(t("failed_load_roles"));
          setLoadError(t("failed_load_roles"));
        }

        if (departmentsResponse.data?.success && Array.isArray(departmentsResponse.data.data)) {
          setDepartments(departmentsResponse.data.data);
        } else {
          toastMessageError(t("failed_load_departments"));
          setLoadError(t("failed_load_departments"));
        }
      } catch (error) {
        toastMessageError((error as string) || t("failed_load_data"));
        setLoadError(t("failed_load_data"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [t]);

  const { fields, append, remove } = useFieldArray({
    control,
    name: "employees",
  });

  const handleAddEmployee = () => {
    // When adding a new employee, use the same interview order as the first one
    append({
      firstName: "",
      lastName: "",
      email: "",
      department: departmentData?.id ? +departmentData?.id : 0,
      role: 0,
    });
  };

  const onSubmit = async (data: { employees: EmployeeForm[] }) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);

      // Transform the data to match the backend validation requirements
      const employees = data.employees.map((employee) => ({
        // Normalize firstName and lastName to remove extra spaces
        firstName: normalizeSpaces(employee.firstName),
        lastName: normalizeSpaces(employee.lastName),
        email: employee.email.trim(), // Just trim email, no need to normalize spaces
        departmentId: employee.department, // Rename department to departmentId
        roleId: employee.role, // Rename role to roleId
      }));

      const response = await addEmployees(employees);

      // Cast the response to our defined type
      const apiResponse = response.data;

      if (apiResponse && apiResponse.success) {
        // Set employee statuses from API response
        if (apiResponse?.data?.employeeStatuses && Array.isArray(apiResponse.data.employeeStatuses)) {
          setEmployeeStatuses(apiResponse.data.employeeStatuses);
          setShowResults(true);
           const allSuccessful = apiResponse.data.employeeStatuses.every(status => status.status === true);
           if (allSuccessful) {
            router.push(ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT);
          }
        }
      } else {
        // Handle API error response
        const errorMessage = apiResponse?.message || "failed_add_employees";
        setSubmitError(translate(errorMessage));
        toastMessageError(translate(errorMessage));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("unexpected_error"));
      setSubmitError(t("unexpected_error"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to get status for an employee by email and index
  const getEmployeeStatus = (email: string, index: number) => {
    if (!showResults) return null;

    // Find the status for this specific email at this specific index
    const status = employeeStatuses[index];

    console.log("status=========>>>>>", status);

    // Only return the status if it matches the current email
    // This ensures we're showing status for the right employee form
    return status && status.email === email ? status : null;
  };

  return (
    <>
      <section className={styles.access_management}>
        <div className="container">
          <div className="row">
            <div className={`${styles.add_employee_height}`}>
              <div className="common-page-header">
                <div className="common-page-head-section">
                  <div className="main-heading">
                    <h2>{t("add_employees")}</h2>
                  </div>
                </div>
              </div>

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="pb-5">
                  {fields.map((field, index) => (
                    <div key={field.id} className={`${styles.form_card} ${index > 0 ? "mb-4" : ""} position-relative mt-0`}>
                      <div className="d-flex justify-content-between mb-3">
                        <div className="d-flex align-items-center justify-content-between w-100">
                          <h4 className="mb-3 color-dark">
                            <strong>{t("employee_number", { number: index + 1 })}</strong>
                          </h4>
                          <div className="d-flex ms-3">
                            {fields.length > 1 && (
                              <Button
                                type="button"
                                disabled={isSubmitting}
                                onClick={() => remove(index)}
                                className="clear-btn p-0"
                                title={t("remove_employee")}
                              >
                                <DeleteIcon className="p-2" />
                              </Button>
                            )}
                          </div>
                        </div>
                        <div className="d-flex align-items-center">
                          {showResults &&
                            (() => {
                              const fieldValue = control._formValues?.employees?.[index]?.email || "";
                              const status = getEmployeeStatus(fieldValue, index);
                              if (!status) return null;

                              return (
                                <div className="d-flex align-items-center">
                                  {status.status === true ? (
                                    <div style={{ color: "green", display: "flex", alignItems: "center" }}>
                                      <FaCheckCircle size={18} className="me-2" />
                                      <span className="font14">{translate(status.message)}</span>
                                    </div>
                                  ) : (
                                    <div style={{ color: "red", display: "flex", alignItems: "center" }}>
                                      <FaTimesCircle size={18} className="me-2" />
                                      <span className="font14">{translate(status.message)}</span>
                                    </div>
                                  )}
                                </div>
                              );
                            })()}
                        </div>
                      </div>
                      {/* {!minimizedForms[index] &&  */}
                      {
                        <div className="row">
                          <div className="col-md-6">
                            <div className="row">
                              <div className="col-md-6">
                                <InputWrapper>
                                  <InputWrapper.Label htmlFor={`employees.${index}.firstName`} required className="fw-bold">
                                    {t("first_name")}
                                  </InputWrapper.Label>
                                  <Textbox
                                    className="form-control"
                                    control={control}
                                    name={`employees.${index}.firstName`}
                                    type="text"
                                    placeholder={t("enter_first_name")}
                                    disabled={isSubmitting}
                                  />
                                  <InputWrapper.Error message={(errors?.employees?.[index]?.firstName?.message as string) || ""} />
                                </InputWrapper>
                              </div>
                              <div className="col-md-6">
                                <InputWrapper>
                                  <InputWrapper.Label htmlFor={`employees.${index}.lastName`} required className="fw-bold">
                                    {t("last_name")}
                                  </InputWrapper.Label>
                                  <Textbox
                                    className="form-control"
                                    control={control}
                                    name={`employees.${index}.lastName`}
                                    type="text"
                                    placeholder={t("enter_last_name")}
                                    disabled={isSubmitting}
                                  />
                                  <InputWrapper.Error message={(errors?.employees?.[index]?.lastName?.message as string) || ""} />
                                </InputWrapper>
                              </div>
                            </div>
                          </div>

                          <div className="col-md-6">
                            <InputWrapper>
                              <InputWrapper.Label htmlFor={`employees.${index}.email`} required className="fw-bold">
                                {t("email")}
                              </InputWrapper.Label>
                              <div className="position-relative">
                                <Textbox
                                  className="form-control"
                                  control={control}
                                  name={`employees.${index}.email`}
                                  type="email"
                                  placeholder={t("enter_email")}
                                  disabled={isSubmitting}
                                />
                              </div>
                              <InputWrapper.Error message={(errors?.employees?.[index]?.email?.message as string) || ""} />
                            </InputWrapper>
                          </div>

                          <div className="col-md-6">
                            <InputWrapper>
                              <InputWrapper.Label htmlFor={`employees.${index}.department`} required className="fw-bold">
                                {t("department")}
                              </InputWrapper.Label>
                              <div className="icon-align">
                                <Select
                                  control={control}
                                  name={`employees.${index}.department`}
                                  options={departments.map((dept) => ({ label: dept.name, value: dept.id }))}
                                  className="w-100"
                                  isLoading={isLoading}
                                  placeholder={t("select_department")}
                                  disabled={isSubmitting}
                                />
                              </div>
                              <InputWrapper.Error message={(errors?.employees?.[index]?.department?.message as string) || ""} />
                            </InputWrapper>
                          </div>

                          <div className="col-md-6">
                            <InputWrapper>
                              <InputWrapper.Label htmlFor={`employees.${index}.role`} required className="fw-bold">
                                {t("role")}
                              </InputWrapper.Label>
                              <div className="icon-align">
                                <Select
                                  control={control}
                                  name={`employees.${index}.role`}
                                  options={roles.map((role) => ({ label: role.name, value: role.id }))}
                                  className="w-100"
                                  isLoading={isLoading}
                                  placeholder={t("select_role")}
                                  disabled={isSubmitting}
                                />
                              </div>
                              <InputWrapper.Error message={(errors?.employees?.[index]?.role?.message as string) || ""} />
                            </InputWrapper>
                          </div>
                        </div>
                      }
                    </div>
                  ))}
                  <Button type="button" className="clear-btn p-0 color-primary mt-2" onClick={handleAddEmployee} disabled={isSubmitting}>
                    {t("add_another_employee")}
                  </Button>
                </div>

                {/* {(submitError || loadError) && (
                  <div className="alert alert-danger mt-3" role="alert">
                    {submitError || loadError}
                  </div>
                )} */}

                <div className="button-align mt-5 minWidth">
                  <Button type="submit" className="primary-btn rounded-md" disabled={isSubmitting || isLoading}>
                    {isSubmitting ? (
                      <>
                        <Loader /> <span className={isSubmitting ? "ms-2" : ""}>{t("adding")}</span>
                      </>
                    ) : (
                      t("employee_add_btn")
                    )}
                  </Button>
                  <Button
                    type="button"
                    className="dark-outline-btn rounded-md minWidth"
                    onClick={() =>
                      router.push(
                        `${ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT_DETAIL}?departmentId=${departmentData?.id}&departmentName=${encodeURIComponent(departmentData?.name || "")}`
                      )
                    }
                    disabled={isSubmitting || isLoading}
                  >
                    {t("cancel")}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default AddEmployee;
