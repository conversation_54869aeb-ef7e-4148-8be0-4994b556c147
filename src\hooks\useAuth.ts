import { useState, useEffect } from "react";

export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        setIsLoading(true);

        // Use the API endpoint that uses the same logic as your middleware
        const response = await fetch("/api/auth/status", {
          method: "GET",
          credentials: "include", // Include cookies in the request
        });

        if (response.ok) {
          const data = await response.json();
          console.log("🔍 Auth status from API:", data);
          setIsAuthenticated(data.isAuthenticated);
        } else {
          console.error("Failed to check auth status:", response.status);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error("Error checking auth status:", error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();

    // Optional: Check periodically for auth changes
    const interval = setInterval(checkAuthStatus, 30000); // Check every 30 seconds

    // Optional: Listen for focus events to check auth when user returns to tab
    const handleFocus = () => {
      checkAuthStatus();
    };

    window.addEventListener("focus", handleFocus);

    return () => {
      clearInterval(interval);
      window.removeEventListener("focus", handleFocus);
    };
  }, []);

  return { isAuthenticated, isLoading };
};
