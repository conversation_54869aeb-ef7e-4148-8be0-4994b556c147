import React, { useCallback, useEffect, useState } from "react";

import { AddQuestionModal } from "@/components/commonModals/AddQuestionModal";
import FinalAssessmentModal from "@/components/commonModals/FinalAssessmentModal";
import Button from "@/components/formElements/Button";
import ArrowDownIcon from "@/components/svgComponents/ArrowDownIcon";
import ShareIcon from "@/components/svgComponents/ShareIcon";
import { AssessmentData, Question } from "@/interfaces/finalAssessment";
import { getFinalAssessmentQuestions } from "@/services/assessmentService";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";

import style from "../../../styles/conductInterview.module.scss";
import { commonConstants } from "@/constants/commonConstants";
import { useTranslations } from "next-intl";
import Loader from "@/components/loader/Loader";
import router from "next/router";
import ROUTES from "@/constants/routes";

const FinalAssessment = () => {
  console.log("🚀 FinalAssessment component initialized");

  const t = useTranslations();
  const [loading, setLoading] = useState(false);
  const [assessmentData, setAssessmentData] = useState<AssessmentData | null>(null);
  const [currentGroupIndex, setCurrentGroupIndex] = useState(0);
  const [expandedQuestions, setExpandedQuestions] = useState<{ [key: number]: boolean }>({});
  const [finalAssessmentId, setFinalAssessmentId] = useState<number | null>(null);
  const [jobId, setJobId] = useState<number | null>(null);
  const [jobApplicationId, setJobApplicationId] = useState<number | null>(null);
  const [isShared, setIsShared] = useState<boolean>(false);
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);

  // State for AddQuestionModal
  const [showAddQuestionModal, setShowAddQuestionModal] = useState(false);
  const [selectedSkill, setSelectedSkill] = useState<{ id: number; title: string } | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);

  // Debug state changes
  console.log("📊 Current State:", {
    loading,
    currentGroupIndex,
    finalAssessmentId,
    jobId,
    jobApplicationId,
    isShared,
    isSubmitted,
    assessmentDataExists: !!assessmentData,
    assessmentDataGroups: assessmentData?.questionGroups?.length || 0,
    expandedQuestionsCount: Object.keys(expandedQuestions).length,
    showAddQuestionModal,
    showShareModal,
    selectedSkill
  });
  // Get parameters from URL on component mount
  useEffect(() => {
    console.log("🔗 URL Parameters Effect - Starting to parse URL parameters");

    // Get parameters from the URL
    const urlParams = new URLSearchParams(window.location.search);
    const finalAssessmentId = urlParams.get(commonConstants.finalAssessmentId);
    const jobId = urlParams.get(commonConstants.jobId);
    const jobApplicationId = urlParams.get(commonConstants.jobApplicationId);

    console.log("🔍 URL Parameters extracted:", {
      currentURL: window.location.href,
      searchParams: window.location.search,
      finalAssessmentId,
      jobId,
      jobApplicationId,
      constants: {
        finalAssessmentIdKey: commonConstants.finalAssessmentId,
        jobIdKey: commonConstants.jobId,
        jobApplicationIdKey: commonConstants.jobApplicationId
      }
    });

    if (finalAssessmentId && !isNaN(+finalAssessmentId) && jobId && !isNaN(+jobId) && jobApplicationId && !isNaN(+jobApplicationId)) {
      console.log("✅ Valid URL parameters found, setting state:", {
        finalAssessmentId: +finalAssessmentId,
        jobId: +jobId,
        jobApplicationId: +jobApplicationId
      });

      setFinalAssessmentId(+finalAssessmentId);
      setJobId(+jobId);
      setJobApplicationId(+jobApplicationId);
    } else {
      console.error("❌ Invalid URL parameters:", {
        finalAssessmentId,
        jobId,
        jobApplicationId,
        validationResults: {
          finalAssessmentIdValid: finalAssessmentId && !isNaN(+finalAssessmentId),
          jobIdValid: jobId && !isNaN(+jobId),
          jobApplicationIdValid: jobApplicationId && !isNaN(+jobApplicationId)
        }
      });

      toastMessageError(t("no_final_assessment_id_found_in_url"));
      return;
    }
  }, [t]);

  // Function to fetch assessment questions - extracted as a reusable function
  const fetchAssessmentQuestions = useCallback(async () => {
    console.log("📡 fetchAssessmentQuestions - Starting API call");
    console.log("🔍 Parameters check:", {
      finalAssessmentId,
      jobId,
      jobApplicationId,
      allParametersValid: !!(finalAssessmentId && jobId && jobApplicationId)
    });

    if (!finalAssessmentId || !jobId || !jobApplicationId) {
      console.error("❌ Missing required parameters for API call:", {
        finalAssessmentId,
        jobId,
        jobApplicationId
      });
      toastMessageError(t("invalid_or_missing_final_assessment_id"));
      return;
    }

    try {
      console.log("⏳ Setting loading to true and making API call...");
      setLoading(true);

      const response = await getFinalAssessmentQuestions(finalAssessmentId, jobId, jobApplicationId);

      console.log("📥 API Response received:", {
        responseExists: !!response,
        dataExists: !!response?.data,
        success: response?.data?.success,
        dataContent: response?.data?.data ? "Data present" : "No data",
        fullResponse: response
      });

      if (response.data && response.data.success) {
        console.log("✅ Successful API response, processing data...");
        console.log("📊 Assessment Data Details:", {
          questionGroups: response.data.data.questionGroups?.length || 0,
          isAssessmentShared: response.data.data.isAssessmentShared,
          isAssessmentSubmitted: response.data.data.isAssessmentSubmitted,
          jobApplicationId: response.data.data.jobApplicationId,
          firstGroupQuestions: response.data.data.questionGroups?.[0]?.questions?.length || 0
        });

        setAssessmentData(response.data.data);

        // Set jobApplicationId from response data
        if (response.data.data.jobApplicationId) {
          console.log("🔄 Updating jobApplicationId from response:", response.data.data.jobApplicationId);
          setJobApplicationId(response.data.data.jobApplicationId);
        }

        // Initialize all questions as expanded
        const questions = response.data.data.questionGroups?.[0]?.questions || [];
        console.log("🔧 Initializing expanded questions state:", {
          questionsCount: questions.length,
          questionIds: questions.map((q: Question) => q.id)
        });

        if (questions.length > 0) {
          const initialExpandState = Object.fromEntries(questions.map((question: Question) => [question.id, true]));
          console.log("📝 Setting expanded questions:", initialExpandState);
          setExpandedQuestions(initialExpandState);
        }

        // Update isShared state based on the latest data
        if (response.data.data.isAssessmentShared !== undefined && response.data.data.isAssessmentSubmitted !== undefined) {
          console.log("🔄 Updating shared/submitted states:", {
            isShared: response.data.data.isAssessmentShared,
            isSubmitted: response.data.data.isAssessmentSubmitted
          });
          setIsShared(response.data.data.isAssessmentShared);
          setIsSubmitted(response.data.data.isAssessmentSubmitted);
        }
      } else {
        console.error("❌ API call failed or returned unsuccessful response:", {
          success: response?.data?.success,
          message: response?.data?.message,
          fullResponse: response
        });
        toastMessageError(t(response.data?.message || "failed_to_fetch_assessment_questions"));
      }
    } catch (error) {
      console.error("💥 Exception in fetchAssessmentQuestions:", error);
      console.error("Error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : "No stack trace"
      });
      toastMessageError(t("failed_to_fetch_assessment_questions"));
    } finally {
      console.log("🏁 fetchAssessmentQuestions completed, setting loading to false");
      setLoading(false);
    }
  }, [finalAssessmentId, jobId, jobApplicationId, t]);

  const handleShareSuccess = useCallback(() => {
    console.log("🎉 handleShareSuccess - Assessment shared successfully");

    // Update isShared state
    console.log("🔄 Setting isShared to true");
    setIsShared(true);

    // Update URL parameter to reflect the change
    console.log("🔄 Refreshing assessment data after share");

    // Refresh the assessment data
    fetchAssessmentQuestions();

    // Close the share modal
    console.log("❌ Closing share modal");
    setShowShareModal(false);
  }, [fetchAssessmentQuestions, setIsShared]);

  useEffect(() => {
    console.log("🔄 Final Assessment ID Effect triggered:", {
      finalAssessmentId,
      shouldFetch: !!finalAssessmentId
    });

    if (finalAssessmentId) {
      console.log("✅ Final Assessment ID is valid, calling fetchAssessmentQuestions");
      fetchAssessmentQuestions();
    } else {
      console.log("⏳ Final Assessment ID not yet available, waiting...");
    }
  }, [finalAssessmentId, fetchAssessmentQuestions]);

  // Toggle question expansion
  const handleToggleQuestion = (questionId: number) => {
    console.log("🔄 Toggling question expansion:", {
      questionId,
      currentState: expandedQuestions[questionId],
      newState: !expandedQuestions[questionId]
    });

    setExpandedQuestions((prev) => ({
      ...prev,
      [questionId]: !prev[questionId],
    }));
  };

  // Handle navigation to next question group
  const handleNextGroup = () => {
    console.log("➡️ handleNextGroup - Navigating to next group");
    console.log("📊 Navigation check:", {
      hasAssessmentData: !!assessmentData,
      currentGroupIndex,
      totalGroups: assessmentData?.questionGroups.length || 0,
      canNavigateNext: assessmentData && currentGroupIndex < assessmentData.questionGroups.length - 1
    });

    if (assessmentData && currentGroupIndex < assessmentData.questionGroups.length - 1) {
      const nextIndex = currentGroupIndex + 1;
      console.log("✅ Moving to next group:", {
        fromIndex: currentGroupIndex,
        toIndex: nextIndex
      });

      setCurrentGroupIndex(nextIndex);

      // Initialize expanded state for questions in the next group
      const nextGroupQuestions = assessmentData.questionGroups[nextIndex].questions;
      console.log("🔧 Initializing expanded state for next group:", {
        groupIndex: nextIndex,
        questionsCount: nextGroupQuestions.length,
        questionIds: nextGroupQuestions.map(q => q.id)
      });

      const initialExpandState = nextGroupQuestions.reduce(
        (acc, question) => {
          acc[question.id] = true;
          return acc;
        },
        {} as { [key: number]: boolean }
      );

      console.log("📝 Setting expanded questions for next group:", initialExpandState);
      setExpandedQuestions(initialExpandState);
    } else {
      console.log("❌ Cannot navigate to next group - at last group or no data");
    }
  };

  // Handle navigation to previous question group
  const handlePreviousGroup = () => {
    console.log("⬅️ handlePreviousGroup - Navigating to previous group");
    console.log("📊 Navigation check:", {
      hasAssessmentData: !!assessmentData,
      currentGroupIndex,
      canNavigatePrevious: assessmentData && currentGroupIndex > 0
    });

    if (assessmentData && currentGroupIndex > 0) {
      const prevIndex = currentGroupIndex - 1;
      console.log("✅ Moving to previous group:", {
        fromIndex: currentGroupIndex,
        toIndex: prevIndex
      });

      setCurrentGroupIndex(prevIndex);

      // Initialize expanded state for questions in the previous group
      const prevGroupQuestions = assessmentData.questionGroups[prevIndex].questions;
      console.log("🔧 Initializing expanded state for previous group:", {
        groupIndex: prevIndex,
        questionsCount: prevGroupQuestions.length,
        questionIds: prevGroupQuestions.map(q => q.id)
      });

      const initialExpandState = prevGroupQuestions.reduce(
        (acc, question) => {
          acc[question.id] = true;
          return acc;
        },
        {} as { [key: number]: boolean }
      );

      console.log("📝 Setting expanded questions for previous group:", initialExpandState);
      setExpandedQuestions(initialExpandState);
    } else {
      console.log("❌ Cannot navigate to previous group - at first group or no data");
    }
  };

  // Get current question group
  const currentGroup = assessmentData?.questionGroups[currentGroupIndex];
  console.log("📋 Current Group Details:", {
    currentGroupIndex,
    hasCurrentGroup: !!currentGroup,
    currentGroupType: currentGroup?.type,
    currentGroupQuestionsCount: currentGroup?.questions?.length || 0
  });

  // Format group type for display
  const formatGroupType = (type: string) => {
    const formatted = type
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");

    console.log("🎨 Formatting group type:", { original: type, formatted });
    return formatted;
  };

  // Determine if this is the last group
  const isLastGroup = assessmentData ? currentGroupIndex === assessmentData.questionGroups.length - 1 : false;
  console.log("🏁 Last Group Check:", {
    isLastGroup,
    currentGroupIndex,
    totalGroups: assessmentData?.questionGroups.length || 0
  });

  // Group questions by skillId
  const getQuestionsGroupedBySkill = () => {
    console.log("🔧 Grouping questions by skill for current group");

    if (!currentGroup) {
      console.log("❌ No current group available for skill grouping");
      return [];
    }

    // Group questions by skillId
    const groupedQuestions: { [key: number]: { skillTitle: string; questions: Question[] } } = {};

    currentGroup.questions.forEach((question) => {
      if (!groupedQuestions[question.skillId]) {
        groupedQuestions[question.skillId] = {
          skillTitle: question.skillTitle,
          questions: [],
        };
      }

      groupedQuestions[question.skillId].questions.push(question);
    });

    // Convert to array for rendering
    const result = Object.values(groupedQuestions);
    console.log("📊 Skill Groups Created:", {
      totalSkillGroups: result.length,
      skillGroups: result.map(group => ({
        skillTitle: group.skillTitle,
        questionsCount: group.questions.length
      }))
    });

    return result;
  };

  const skillGroups = getQuestionsGroupedBySkill();

  // Render loading state
  if (loading) {
    console.log("⏳ Rendering loading state");
    return (
      <div className={style.conduct_interview_page}>
        <div className="container">
          <div className="text-center py-5">
            <Loader />
            <h1 className="mt-3">{t("loading_assessment_questions")}</h1>
          </div>
        </div>
      </div>
    );
  }

  // Render error state if no assessment data
  if (!loading && !assessmentData) {
    console.log("❌ Rendering error state - no assessment data found");
    return (
      <div className={style.conduct_interview_page}>
        <div className="container">
          <div className="text-center py-5">{t("no_assessment_data_found")}</div>
        </div>
      </div>
    );
  }

  // Handle modal actions
  const handleCloseAddQuestionModal = () => {
    console.log("❌ Closing Add Question Modal");
    setShowAddQuestionModal(false);
    setSelectedSkill(null);
  };

  const handleAddQuestionSuccess = () => {
    console.log("🎉 Question added successfully, refreshing data");
    toastMessageSuccess(t("question_added_successfully"));
    setShowAddQuestionModal(false);
    setSelectedSkill(null);

    // Re-fetch assessment questions
    fetchAssessmentQuestions();
  };

  console.log("🎨 Rendering main component with data:", {
    hasAssessmentData: !!assessmentData,
    currentGroupIndex,
    skillGroupsCount: skillGroups.length,
    isShared,
    isSubmitted,
    isLastGroup,
    showAddQuestionModal,
    showShareModal
  });

  return (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                Final <span>Assessment</span>
              </h2>
            </div>
          </div>
        </div>
        <div className="inner-section">
          <div className="section-heading d-flex justify-content-between mb-4">
            <h2 className="m-0">
              {currentGroup && (
                <>
                  {t("group")} {currentGroupIndex + 1} of {assessmentData?.questionGroups.length}: <span>{formatGroupType(currentGroup.type)}</span>
                </>
              )}
            </h2>
            {!isShared && !isSubmitted ? (
              <Button className="clear-btn text-btn primary p-0 m-0" onClick={() => {
                console.log("📤 Share button clicked - opening share modal");
                setShowShareModal(true);
              }}>
                <ShareIcon className="me-2" />
                {t("share_assessment_link_to_candidate")}
              </Button>
            ) : (
              <div className="d-flex align-items-center">
                <div className="color-legend d-flex align-items-center">
                  <div className="d-flex align-items-center me-3">
                    <div style={{ width: "15px", height: "15px", backgroundColor: "#f5b759", borderRadius: "3px", marginRight: "5px" }}></div>
                    <span style={{ fontSize: "12px" }}>{t("candidate_answer")}</span>
                  </div>
                  <div className="d-flex align-items-center">
                    <div style={{ width: "15px", height: "15px", backgroundColor: "#00a651", borderRadius: "3px", marginRight: "5px" }}></div>
                    <span style={{ fontSize: "12px" }}>{t("correct_answer")}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="row">
            <div className="col-md-12">
              {skillGroups.map((skillGroup, skillGroupIndex) => (
                <div key={`skill-${skillGroupIndex}`} className="skill-group mb-4">
                  <h3
                    className="skill-title mb-3"
                    style={{
                      color: "#131313",
                      borderTop: skillGroupIndex === 0 ? "" : "1px dashed #e3e6f0",
                      paddingTop: skillGroupIndex === 0 ? "10px" : "20px",
                      paddingBottom: "10px",
                      marginTop: skillGroupIndex === 0 ? "0" : "20px",
                      fontSize: "20px",
                      fontWeight: "600",
                    }}
                  >
                    {t("skill")}: {skillGroup.skillTitle}
                  </h3>

                  {skillGroup.questions.map((question, index) => (
                    <div key={question.id} className="interview-question-card with-border" onClick={() => handleToggleQuestion(question.id)}>
                      <p className="tittle">
                        {t("question")} {index + 1} <ArrowDownIcon className={!expandedQuestions[question.id] ? "rotate" : ""} />
                      </p>
                      <h5>{question.question}</h5>
                      {expandedQuestions[question.id] && (
                        <div className="question-body" onClick={(e) => e.stopPropagation()}>
                          {question.options.options.map((option) => {
                            console.log("🎯 Processing option for question:", {
                              questionId: question.id,
                              optionId: option.id,
                              optionText: option.text,
                              correctAnswer: question.correctAnswer,
                              applicantAnswer: question.applicantAnswer
                            });

                            const isCorrectAnswer = option.id === question.correctAnswer;
                            const isCandidateAnswer = option.id === question.applicantAnswer;

                            console.log("✅ Option analysis:", {
                              optionId: option.id,
                              isCorrectAnswer,
                              isCandidateAnswer,
                              bothCorrectAndCandidate: isCandidateAnswer === isCorrectAnswer
                            });
                            isBothCorrectAndCandidate = isCandidateAnswer && isCorrectAnswer;
                            return (
                              <div
                                key={option.id}
                                className={`answer-strap ${
                                  isCandidateAnswer === isCorrectAnswer
                                    ? isCorrectAnswer
                                      ? "right-answer"
                                      : ""
                                    : isCorrectAnswer
                                      ? "right-answer"
                                      : isCandidateAnswer
                                        ? "candidate-answer"
                                        : ""
                                }`}
                              >
                                <div className="radio-wrapper">
                                  <input
                                    className="radio-input form-check-input"
                                    type="radio"
                                    name={`question_${question.id}`}
                                    id={`question_${question.id}_option_${option.id}`}
                                    value={option.id}
                                    checked={isBothCorrectAndCandidate}
                                    readOnly
                                  />
                                  <label className="radio-label" htmlFor={`question_${question.id}_option_${option.id}`}>
                                    {option.text}
                                  </label>
                                </div>
                              </div>
                            );
                          })}
                          {/* <div className="answer-strap p-0 border-0">
                            <p className="note-text">Note: The candidate's answer is not visible to you.</p>
                          </div> */}
                        </div>
                      )}
                    </div>
                  ))}

                  {/* Add New Question button for each skill group - only show if not shared */}
                  {!isShared && !isSubmitted && (
                    <Button
                      className="clear-btn text-btn secondary p-0 mb-3 mt-2"
                      style={{ color: "#f5b759", fontWeight: 500 }}
                      onClick={() => {
                        setSelectedSkill({
                          id: skillGroup.questions[0].skillId, // Get skillId from the first question in the group
                          title: skillGroup.skillTitle,
                        });
                        setShowAddQuestionModal(true);
                      }}
                    >
                      {t("add_new_question")}
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="button-align" style={{ display: "flex", justifyContent: "space-between" }}>
          {/* Show Previous button if not on the first group */}
          <div>
            {currentGroupIndex > 0 && (
              <Button className="secondary-btn rounded-md" onClick={handlePreviousGroup}>
                {t("previous_skill_assessment")}
              </Button>
            )}
          </div>

          {/* Show Next button if not on the last group, positioned on the right */}
          <div>
            {!isLastGroup ? (
              <Button className="primary-btn rounded-md" onClick={handleNextGroup}>
                {t("next_skill_assessment")}
              </Button>
            ) : !isSubmitted ? (
              <Button
                className="primary-btn rounded-md"
                onClick={() => {
                  // setShowShareModal(true);
                  router.push(`${ROUTES.JOBS.CANDIDATE_PROFILE}/${jobApplicationId}`); // Navigate to candidate profile
                  // We'll navigate after sharing or if user cancels
                }}
              >
                {t("complete_assessment")}
              </Button>
            ) : null}
          </div>
        </div>
      </div>

      {/* Add Question Modal */}
      {showAddQuestionModal && selectedSkill && finalAssessmentId && (
        <AddQuestionModal
          onClickCancel={handleCloseAddQuestionModal}
          onSubmitSuccess={handleAddQuestionSuccess}
          skillId={selectedSkill.id}
          skillTitle={selectedSkill.title}
          finalAssessmentId={Number(finalAssessmentId)}
        />
      )}

      {/* Share Assessment Modal */}
      {showShareModal && finalAssessmentId && (
        <FinalAssessmentModal
          onClickCancel={() => setShowShareModal(false)}
          onSubmitSuccess={handleShareSuccess}
          finalAssessmentId={Number(finalAssessmentId)}
          jobApplicationId={Number(jobApplicationId)}
          isFromCompleteButton={isLastGroup}
        />
      )}
    </div>
  );
};

export default FinalAssessment;
