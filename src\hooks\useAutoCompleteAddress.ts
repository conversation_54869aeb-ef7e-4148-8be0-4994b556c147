// import { useCallback, useEffect, useState } from "react";

// import { debounce, isEmpty } from "lodash";
// import { Prediction, IUseAutoCompleteAddress } from "@/interfaces/commonInterfaces";
// import { fetchPredictions } from "@/utils/helper";

// const useAutoCompleteAddress = (
//   locationName: string
// ): IUseAutoCompleteAddress => {
//   const [searchText, setSearchText] = useState<string>();
//   const [predictions, setPredictions] = useState<Prediction[]>([]);

//   useEffect(() => {
//     (async () => {
//       // eslint-disable-next-line no-unused-expressions
//       !isEmpty(searchText)
//         ? await getPredictions(searchText as string)
//         : setPredictions([]);
//     })();
//   }, [searchText]);

//   const delayedLocationChange = useCallback(
//     debounce((name: string) => {
//       setSearchText(name || undefined);
//     }, 500),
//     []
//   );

//   useEffect(() => {
//     delayedLocationChange.cancel();
//     delayedLocationChange(locationName);
//   }, [locationName]);

//   /**
//    * fetch predictions of address
//    */
//   const getPredictions = async (name: string) => {
//     // const res: IPredictionsResponse = await fetchPredictions(name);
//     // if (res?.success) {
//     //   setPredictions(res.data.predictions);
//     // }
//   };

//   return {
//     predictions,
//   };
// };

// export default useAutoCompleteAddress;
