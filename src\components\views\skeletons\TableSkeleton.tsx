import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const TableSkeleton = ({ rows = 3, cols = 3, colWidths = "120,80,100" }) => {
  const columnWidths = colWidths.split(",").map((w) => w.trim());

  console.log("inside skeletons======");

  return (
    <tbody>
      {[...Array(rows)].map((_, rowIndex) => (
        <tr key={`loader-row-${rowIndex}`}>
          {[...Array(cols)].map((_, colIndex) => (
            <td key={`loader-col-${colIndex}`} className="text-center">
              <Skeleton width={columnWidths[colIndex] || 80} height={20} circle={false} />
            </td>
          ))}
        </tr>
      ))}
    </tbody>
  );
};

export default TableSkeleton;
