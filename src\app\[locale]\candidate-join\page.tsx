"use client";
import React from "react";
import CandidateJoin from "@/components/views/conductInterview/CandidateJoin";
import AgoraRTC, { AgoraRTCProvider } from "agora-rtc-react";
const page = () => {
  return (
    <div>
      <AgoraRTCProvider client={AgoraRTC.createClient({ mode: "rtc", codec: "vp8" })}>
        <CandidateJoin />
      </AgoraRTCProvider>
    </div>
  );
};

export default page;
