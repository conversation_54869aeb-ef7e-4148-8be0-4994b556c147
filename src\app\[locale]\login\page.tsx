"use client";
import { yupResolver } from "@hookform/resolvers/yup";
import { getSession, signIn } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import routes from "@/constants/routes";
import { ILogin } from "@/interfaces/authInterfaces";
import { setAuthData, setPermissions, setRole, setDepartment, setCurrentPlan } from "@/redux/slices/authSlice";
import styles from "@/styles/auth.module.scss";
import { setAccessToken, toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { loginValidation } from "@/validations/authValidations";
import logo from "../../../../public/assets/images/logo.svg";

import hidePasswordIcon from "../../../../public/assets/images/hide-password.svg";
import showPasswordIcon from "../../../../public/assets/images/show-password.svg";
import { updateTimezone } from "@/services/authServices";
import { ISession } from "@/interfaces/commonInterfaces";
import { syncReduxStateToCookies } from "@/utils/syncReduxToCookies";
import { useTranslate } from "@/utils/translationUtils";
import ROUTES from "@/constants/routes";

const Login = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const translate = useTranslate();
  const navigate = useRouter();

  const router = useRouter();
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(loginValidation(translate)),
  });

  const submit = async (data: ILogin) => {
    try {
      setLoading(true);
      const response = await signIn("credentials", {
        redirect: false,
        email: data?.email,
        password: data?.password,
        callbackUrl: `${window.location.origin}`,
      });
      console.log("Login response:", response);

      if (response?.ok) {
        const session = (await getSession()) as unknown as ISession;
        console.log(">>>>>>>>>>>>>>>>>>session");
        console.log(session);

        if (session?.user?.success) {
          const userData = session?.user?.data?.authData?.userData;
          const roleData = session?.user?.data?.authData?.role;
          const permissionData = session?.user?.data?.authData?.permissions;
          const departmentData = session?.user?.data?.authData?.department;
          const planData = session?.user?.data?.authData?.currentPlan;

          dispatch(setPermissions(permissionData));
          updateTimezone({
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          });
          syncReduxStateToCookies(permissionData, true);
          setAccessToken(session?.user?.data?.token);

          dispatch(setAuthData(userData));
          dispatch(setRole(roleData));
          dispatch(setDepartment(departmentData));

          // Save current plan data to Redux
          if (planData) {
            dispatch(setCurrentPlan(planData));
          }
          toastMessageSuccess(translate(session?.user?.message as string));
          router.replace(routes.DASHBOARD);
        } else {
          toastMessageError(translate((session?.user?.message as string) || "something_went_wrong"));
        }
      } else {
        // Handle authentication errors with specific backend messages
        const errorMessage = response?.error || "something_went_wrong";
        console.log("❌ Login failed with error:", errorMessage);
        toastMessageError(translate(errorMessage));
      }
    } catch (error) {
      console.error("❌ Login error:", error);
      toastMessageError(translate("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.auth_main}>
      <div className="container">
        <div className="row">
          <div className={styles.user_auth_main}>
            <div className="container">
              <div className="row row-center">
                <div className={`${styles.hero_image} col-md-6`} />
                <div className="col-md-6">
                  <div className={styles.form_main}>
                    <div className="text-center" onClick={() => navigate.push(ROUTES.HOME)}>
                      <Image src={logo} alt="logo" className={styles.logo} width={200} height={80} />
                      <h1>
                        <span>{translate("hello")}</span> {translate("welcome_back")}
                      </h1>
                    </div>
                    <form onSubmit={handleSubmit(submit)}>
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="email" required>
                          {translate("email")}
                        </InputWrapper.Label>
                        <Textbox
                          className="form-control"
                          control={control}
                          name="email"
                          type="text"
                          placeholder={translate("enter_your_email")}
                        ></Textbox>
                        <InputWrapper.Error message={errors?.email?.message || ""} />
                      </InputWrapper>

                      <InputWrapper>
                        <InputWrapper.Label htmlFor="password" required>
                          {translate("password")}
                        </InputWrapper.Label>
                        <Textbox
                          className="form-control"
                          control={control}
                          name="password"
                          iconClass="icon-align"
                          align="right"
                          type={!showPassword ? "password" : "text"}
                          placeholder={translate("enter_your_password")}
                        >
                          <InputWrapper.Icon onClick={() => setShowPassword(!showPassword)}>
                            <Image src={showPassword ? showPasswordIcon : hidePasswordIcon} alt="password-icon" />
                          </InputWrapper.Icon>
                        </Textbox>
                        <InputWrapper.Error message={errors?.password?.message || ""} />
                      </InputWrapper>

                      <div className="reminder-align">
                        <Link href={routes.FORGOT_PASSWORD} className="forgot-password">
                          {translate("forgot_password")}
                        </Link>
                      </div>
                      <Button disabled={loading} loading={loading} className="primary-btn rounded-md w-100 mt-5">
                        {translate("login")}
                      </Button>

                      <div className="text-center mt-3">
                        <p>
                          {translate("dont_have_an_account")}{" "}
                          <Link href={routes.SIGNUP} className={styles.signup_link}>
                            {translate("sign_up")}
                          </Link>
                        </p>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
