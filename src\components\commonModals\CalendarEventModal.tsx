/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-expressions */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable @typescript-eslint/no-explicit-any */
// src/components/commonModals/CalenderEventModal.tsx
"use client";
import React, { FormEventHandler, useCallback, useEffect, useRef, useState } from "react";
import { Control, FieldErrors, UseFormGetValues, UseFormSetError, UseFormSetValue } from "react-hook-form";
import { debounce } from "lodash";
import toast from "react-hot-toast";
import { useTranslations } from "next-intl";

import "../../styles/eventModal.scss";
import InputWrapper from "../formElements/InputWrapper";
import Textbox from "../formElements/Textbox";
// import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Button from "../formElements/Button";
import {
  IGetCandidateListResponse,
  IGetInterviewsResponse,
  IGetJobListResponse,
  ScheduleInterviewFormValues,
} from "@/interfaces/interviewInterfaces";
import ReactCommonSelect from "../formElements/ReactCommonSelect";
import CommonDatePickerWrapper from "../formElements/CommonDatepicker";
import Textarea from "../formElements/Textarea";
import { FILE_EXTENSION, INTERVIEW_SCHEDULE_ROUND_TYPE, ScheduleInterviewFormSubmissionType, S3_PATHS } from "@/constants/commonConstants";
import { toastMessageError, uploadFileOnS3 } from "@/utils/helper";
import UploadBox from "../commonComponent/UploadBox";
import UploadFileIcon from "../svgComponents/UploadFileIcon";
import DeleteDarkIcon from "../svgComponents/DeleteDarkIcon";
import Select from "../formElements/Select";
import { getCandidateList } from "@/services/interviewServices";
import CommonTimePicker from "../formElements/CommonTimePicker";
import { removeAttachmentsFromS3 } from "@/services/commonService";
import { INTERVIEW_SCHEDULING_MESSAGES } from "@/constants/jobRequirementConstant";

interface EventModalProps {
  onClose: () => void;
  handleSubmit: FormEventHandler<HTMLFormElement>;
  debouncedHandleSearchInputChange: (value: string) => void;
  setFileUrls: React.Dispatch<React.SetStateAction<string[]>>;
  fileUrls: string[];
  control: Control<ScheduleInterviewFormValues | any>;
  loading: boolean;
  errors: FieldErrors<ScheduleInterviewFormValues>;
  currentFileArrayLengthRef: React.RefObject<number>;
  interviewers: Array<{ label: string; value: number }>;
  loader: boolean;
  formType: string;
  getValues: UseFormGetValues<ScheduleInterviewFormValues>;
  setValue: UseFormSetValue<ScheduleInterviewFormValues>;
  setError: UseFormSetError<ScheduleInterviewFormValues>;
  setJobs: React.Dispatch<React.SetStateAction<Array<IGetJobListResponse>>>;
  interviewInfo: IGetInterviewsResponse | null;

  debouncedHandleJobSearchInputChange?: (value: string) => void;
  jobs?: Array<IGetJobListResponse>;
  jobLoader?: boolean;
  candidateName?: string;
}

const CalendarEventModal: React.FC<EventModalProps> = ({
  onClose,
  control,
  handleSubmit,
  loading,
  errors,
  interviewers,
  jobs,
  loader,
  debouncedHandleSearchInputChange,
  debouncedHandleJobSearchInputChange,
  setFileUrls,
  getValues,
  setValue,
  setError,
  fileUrls,
  jobLoader,
  formType,
  currentFileArrayLengthRef,
  interviewInfo,
  setJobs,
  candidateName,
}) => {
  const t = useTranslations();

  const inputRef = useRef<HTMLInputElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const descriptionRef = useRef<HTMLTextAreaElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [candidateLoading, setCandidateLoading] = useState(false);
  const [candidates, setCandidates] = useState<IGetCandidateListResponse[]>([]);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [formChanged, setFormChanged] = useState(false);

  console.log("fileUrls====", fileUrls);

  const onFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (currentFileArrayLengthRef.current >= 3) {
      toast.dismiss();
      toastMessageError(t("max_files_limit_msg"));
      return;
    }
    setIsLoading(true);
    const { files } = e.target;
    console.log("files", files);
    if (files?.length && Number(files?.[0].size) < 10854484) {
      const file = files[0];
      const uploadedUrls = [...(fileUrls || [])];

      if (file) {
        const Extension = file?.type?.split("/")[1];
        const fileNameArr = file.name.split(".");
        console.log("=================>", fileNameArr[0].replace(/\s+/g, ""));
        const fileName = fileNameArr[0].replace(/\s+/g, "");
        const filePath = `${S3_PATHS.CONDUCT_INTERVIEW}/${fileName}-${new Date().getTime()}.${fileNameArr[1]}`;
        if (FILE_EXTENSION.includes(Extension?.toLowerCase())) {
          const uploadedFileUrl = (await uploadFileOnS3(file, filePath)) as string;
          uploadedUrls.push(uploadedFileUrl);
          setFileUrls((prev) => [...(prev ?? []), uploadedFileUrl]);
          currentFileArrayLengthRef.current++;

          // Set form as changed when a file is uploaded
          if (formType === ScheduleInterviewFormSubmissionType.UPDATE) {
            setFormChanged(true);
          }
        } else {
          toastMessageError(t("invalid_file_format"));
        }
      }
    } else {
      toastMessageError(t("invalid_size_format"));
    }
    if (inputRef.current) {
      inputRef.current.value = "";
    }
    setIsLoading(false);
  };

  const onModalClose = async () => {
    if (inputRef.current) {
      inputRef.current.value = "";
    }
    currentFileArrayLengthRef.current = 0;

    onClose();
    setFileUrls([]);
    fileUrls?.length && (await removeAttachmentsFromS3({ fileUrlArray: JSON.stringify(fileUrls) }));
  };

  const onHandleDelete = async (fileUrl: string) => {
    setFileUrls((prev) => prev && prev.filter((file) => file !== fileUrl));
    currentFileArrayLengthRef.current--;
    await removeAttachmentsFromS3({ fileUrlArray: JSON.stringify(fileUrl) });

    // Set form as changed when a file is deleted
    if (formType === ScheduleInterviewFormSubmissionType.UPDATE) {
      setFormChanged(true);
    }
  };

  const scrollIntoViewWithin = (el: HTMLElement) => {
    console.log("el=====>>>>>>>>>", el);
    const container = scrollAreaRef.current;
    if (!container) return;

    const TOP_OFFSET = 16; // Keep space for sticky headers or top padding
    const elRect = el.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();
    const currentScrollTop = container.scrollTop;
    const targetTop = elRect.top - containerRect.top + currentScrollTop - TOP_OFFSET;

    container.scrollTo({
      top: Math.max(0, targetTop),
      behavior: "smooth",
    });

    // Focus after scrolling
    setTimeout(() => el.focus?.(), 50);
  };

  useEffect(() => {
    const keys = Object.keys(errors || {});
    if (!keys.length) return;

    const firstKey = keys[0];

    // Handle specific fields with refs
    if (firstKey === "description" && descriptionRef.current) {
      scrollIntoViewWithin(descriptionRef.current);
      return;
    }

    // Fall back to DOM queries for other fields
    // 1) Try to find a real DOM node with name
    let target = document.querySelector<HTMLElement>(`[name="${firstKey}"]`);

    // 2) Fallback to wrapper annotated with data-field
    if (!target) {
      target = document.querySelector<HTMLElement>(`[data-field="${firstKey}"]`);
    }

    if (target) {
      console.log("target=====>>>>>>>>>", target);
      scrollIntoViewWithin(target);
    }
  }, [errors]);

  useEffect(() => {
    if (formType === ScheduleInterviewFormSubmissionType.UPDATE && interviewInfo) {
      const candidate = {
        label: interviewInfo?.candidateName,
        value: interviewInfo?.jobApplicationId,
      };
      setCandidates([candidate]);

      setJobs([{ label: interviewInfo.jobTitle, value: interviewInfo.jobId, jobId: interviewInfo.jobUniqueId }]);
    }
    if (!!candidateName) {
      setCandidates([{ label: candidateName, value: getValues("candidate") }]);
    }
  }, []);

  const getCandidates = useCallback(async (searchString: string, jobId?: number) => {
    setCandidateLoading(true);
    try {
      const response = await getCandidateList({
        searchString,
        jobId: jobId?.toString() || "",
      });

      if (response?.data?.success) {
        setCandidates(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching interviewers:", error);
    } finally {
      setCandidateLoading(false);
    }
  }, []);

  const handleCandidateSearchInputChange = (event: string) => {
    const searchString = event.trim();
    console.log("searchString", searchString);
    getCandidates(searchString, getValues("jobTitle"));
  };

  const debouncedHandleCandidateSearchInputChange = debounce(handleCandidateSearchInputChange, 1000);

  // Message rotation effect
  useEffect(() => {
    if (!loading || !INTERVIEW_SCHEDULING_MESSAGES || INTERVIEW_SCHEDULING_MESSAGES.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentMessageIndex((prevIndex) => (prevIndex + 1) % INTERVIEW_SCHEDULING_MESSAGES.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [loading]);

  // Initialize form change detection when in UPDATE mode
  useEffect(() => {
    if (formType === ScheduleInterviewFormSubmissionType.UPDATE) {
      // Store initial form values to compare against
      const initialValues = getValues();
      const initialValuesJson = JSON.stringify(initialValues);

      // Set up an interval to check for changes
      const interval = setInterval(() => {
        const currentValues = getValues();
        const currentValuesJson = JSON.stringify(currentValues);

        if (initialValuesJson !== currentValuesJson) {
          console.log("Form values changed");
          setFormChanged(true);
          clearInterval(interval);
        }
      }, 500);

      // Clean up interval and reset formChanged state when unmounting
      return () => {
        clearInterval(interval);
        setFormChanged(false);
      };
    }

    // Reset formChanged when not in update mode or when unmounting
    return () => {
      setFormChanged(false);
    };
  }, [formType]); // Only depend on formType

  return (
    <>
      <div className="modal theme-modal show-modal modal-lg">
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <form onSubmit={handleSubmit}>
              <div className="modal-header secondary-header">
                <h4 className="m-0">{formType === ScheduleInterviewFormSubmissionType.UPDATE ? t("update_interview") : t("schedule_interview")}</h4>
                {!loading && (
                  <div className="button-align">
                    <Button
                      type="submit"
                      disabled={loading || (formType === ScheduleInterviewFormSubmissionType.UPDATE && !formChanged)}
                      loading={loading}
                      className={`primary-btn rounded-md ${formType === ScheduleInterviewFormSubmissionType.UPDATE && !formChanged ? "truly-disabled" : ""}`}
                    >
                      {formType === ScheduleInterviewFormSubmissionType.UPDATE ? t("update_interview") : t("schedule_interview")}
                    </Button>
                    <Button type="button" onClick={onModalClose} disabled={loading} className="dark-outline-btn rounded-md">
                      {t("cancel")}
                    </Button>
                  </div>
                )}
              </div>
              <div className="modal-body">
                {loading ? (
                  <div className="animated-text-with-loader">
                    <div className="loading-text-container">
                      <p className="loading-text">{INTERVIEW_SCHEDULING_MESSAGES[currentMessageIndex]}</p>
                    </div>
                  </div>
                ) : (
                  <div ref={scrollAreaRef} style={{ maxHeight: "calc(100vh - 350px)", overflowY: "auto", paddingRight: "10px" }}>
                    <div className="row">
                      <div className="col-md-6">
                        <ReactCommonSelect
                          name="jobTitle"
                          required
                          control={control}
                          options={jobs?.length ? jobs : []}
                          isDisabled={formType === ScheduleInterviewFormSubmissionType.UPDATE || !!candidateName || loading}
                          label={t("job_title")}
                          placeholder={t("please_select_job")}
                          onInputChange={(e) => {
                            if (debouncedHandleJobSearchInputChange) debouncedHandleJobSearchInputChange(e);
                          }}
                          onChange={(e) => {
                            setValue("jobId", e?.jobId);
                            // setValue("candidate", undefined);
                            setError("jobId", { type: "manual", message: "" });
                            setCandidates([]);
                            getCandidates("", e?.value);
                          }}
                          // value={formType === ScheduleInterviewFormSubmissionType.UPDATE ? jobs?.[0] : null}
                          isLoading={jobLoader}
                          errors={errors}
                          noOptionMessage={t("no_jobs_found")}
                        />
                      </div>
                      <div className="col-md-6">
                        <InputWrapper>
                          <InputWrapper.Label htmlFor="jobId" required>
                            {t("job_id")}
                          </InputWrapper.Label>
                          <Textbox className="form-control" disabled control={control} name="jobId" type="text" placeholder={t("job_id_desc")} />
                          <InputWrapper.Error message={errors?.jobId?.message || ""} />
                        </InputWrapper>
                      </div>
                    </div>
                    <div data-field="eventTitle">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="eventTitle" required>
                          {t("interview_title")}
                        </InputWrapper.Label>
                        <Textbox
                          className="form-control"
                          control={control}
                          name="eventTitle"
                          type="text"
                          placeholder={t("title_desc")}
                          disabled={loading}
                        />
                        <InputWrapper.Error message={errors?.eventTitle?.message || ""} />
                      </InputWrapper>
                    </div>

                    <ReactCommonSelect
                      name="candidate"
                      required
                      control={control}
                      options={candidates?.length ? candidates : []}
                      isDisabled={(formType !== ScheduleInterviewFormSubmissionType.UPDATE && !!candidateName) || loading}
                      label={t("candidate")}
                      placeholder={t("candidate_placeholder")}
                      onInputChange={(e) => debouncedHandleCandidateSearchInputChange(e)}
                      isLoading={candidateLoading}
                      errors={errors}
                      noOptionMessage={t("no_candidates_found")}
                    />
                    <div data-field="interviewer">
                      <ReactCommonSelect
                        name="interviewer"
                        required
                        control={control}
                        options={interviewers?.length ? interviewers : []}
                        isDisabled={loading}
                        onChange={(val) => {
                          if (!val) {
                            console.log("val", val);
                            // setValue("interviewer", -1);
                          }
                        }}
                        label={t("interviewer")}
                        placeholder={t("please_select_interviewer")}
                        onInputChange={(e) => debouncedHandleSearchInputChange(e)}
                        isLoading={loader}
                        errors={errors}
                        noOptionMessage={t("no_interviewers_found")}
                      />
                    </div>

                    <div className="row">
                      <div className="col-md-6">
                        <div data-field="interviewType">
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="interviewType" required>
                              {t("interview_type")}
                            </InputWrapper.Label>
                            <Select
                              options={INTERVIEW_SCHEDULE_ROUND_TYPE}
                              className="form-control"
                              control={control}
                              name="interviewType"
                              placeholder={t("please_select_interview_type")}
                              disabled={loading}
                            />
                            <InputWrapper.Error message={errors?.interviewType?.message || ""} />
                          </InputWrapper>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <CommonDatePickerWrapper
                          control={control}
                          name="date"
                          label={t("interview_date")}
                          placeholder={t("please_select_date")}
                          error={errors?.date}
                          onBlur={() => {
                            console.log("onBlur");
                          }}
                          dateRange={[]}
                          required
                          defaultDate={new Date()}
                          disabled={loading}
                        />
                      </div>
                    </div>
                    <div className="row">
                      <div className="col-md-6">
                        <InputWrapper>
                          <InputWrapper.Label htmlFor="startTime" required>
                            {t("start_time")}
                          </InputWrapper.Label>
                          <CommonTimePicker
                            name="startTime"
                            className={`form-control ${loading ? "disabled" : ""}`}
                            control={control}
                            disabled={loading}
                          />
                          <InputWrapper.Error message={errors?.startTime?.message || ""} />
                        </InputWrapper>
                      </div>
                      <div className="col-md-6">
                        <InputWrapper>
                          <InputWrapper.Label htmlFor="endTime" required>
                            {t("end_time")}
                          </InputWrapper.Label>
                          <CommonTimePicker
                            name="endTime"
                            className={`form-control ${loading ? "disabled" : ""}`}
                            control={control}
                            disabled={loading}
                          />
                          <InputWrapper.Error message={errors?.endTime?.message || ""} />
                        </InputWrapper>
                      </div>
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="attachments">{t("attachments")}</InputWrapper.Label>
                        <UploadBox
                          UploadBoxClassName="upload-card-sm"
                          onChange={(e) => {
                            console.log("e==================>", e.target.value);
                            onFileChange(e);
                          }}
                          inputRef={inputRef}
                          isLoading={isLoading}
                          disabled={loading}
                        />
                        {/* uploaded-item */}
                        <div className="d-flex flex-wrap">
                          {fileUrls?.length
                            ? fileUrls.map((file) => {
                                const fileName = file?.split("/").pop()?.split("-")[0];
                                const fileExt = file?.split(".").pop();
                                return (
                                  <div className={`uploded-item upload-card-sm me-3 ${loading ? "disabled" : ""}`} key={file} title={fileName}>
                                    <div className="item-name">
                                      <UploadFileIcon />
                                      <p>
                                        {fileName
                                          ? fileName.length > 10
                                            ? `${fileName.slice(0, 10)}...${fileName.slice(fileName.length - 3, fileName.length)}.${fileExt}`
                                            : `${fileName}.${fileExt}`
                                          : `document.${fileExt}`}
                                      </p>
                                    </div>
                                    <DeleteDarkIcon className="delete-item" onClick={() => !isLoading && !loading && onHandleDelete(file)} />
                                  </div>
                                );
                              })
                            : null}
                        </div>
                      </InputWrapper>
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="description">{t("additional_info")}</InputWrapper.Label>
                        <Textarea
                          className="form-control"
                          control={control}
                          name="description"
                          rows={5}
                          placeholder={t("additional_info_desc_")}
                          disabled={loading}
                          ref={descriptionRef}
                        />
                        <InputWrapper.Error message={errors?.description?.message || ""} />
                      </InputWrapper>
                    </div>
                  </div>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default React.memo(CalendarEventModal);
