import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import createMiddleware from "next-intl/middleware";
import { routing } from "./i18n/routing";

import { PERMISSIONS_COOKIES_KEY } from "./constants/commonConstants";
import ROUTES, { BEFORE_LOGIN_ROUTES, UNRESTRICTED_ROUTES } from "./constants/routes";
import { getStaticRoutePermissions, getDynamicRoutePatterns, hasPermissionForRoute, parseCookieData } from "./utils/middlewareHelpers";

// Create intl middleware
const intlMiddleware = createMiddleware(routing);

export default async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;

  // CRITICAL: Skip middleware completely for NextAuth API routes
  if (path.startsWith("/api/auth/")) {
    console.log("🔄 Middleware: Skipping NextAuth API route:", path);
    return;
  }

  // Skip middleware for other API routes
  if (path.startsWith("/api/")) {
    console.log("🔄 Middleware: Skipping API route:", path);
    return intlMiddleware(req);
  }

  try {
    // Get session using next-auth JWT
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    const isAuthenticated = !!token;
    const isPublicRoute = BEFORE_LOGIN_ROUTES.includes(path);
    const isUnrestrictedRoute = UNRESTRICTED_ROUTES.includes(path) || UNRESTRICTED_ROUTES.some((route) => path.startsWith(route + "/"));

    console.log("🔄 Middleware Debug:", {
      path,
      isAuthenticated,
      isPublicRoute,
      isUnrestrictedRoute,
      tokenExists: !!token,
    });

    // Always allow access to home page
    if (path === ROUTES.HOME) {
      return intlMiddleware(req);
    }

    // Handle authenticated users
    if (isAuthenticated) {
      // Redirect authenticated users away from public routes to dashboard
      if (isPublicRoute) {
        console.log("🔄 Redirecting authenticated user from public route to dashboard");
        return NextResponse.redirect(new URL(ROUTES.DASHBOARD, req.url));
      }

      // Allow unrestricted routes without permission check
      if (isUnrestrictedRoute) {
        return intlMiddleware(req);
      }

      // For protected routes, check permissions
      const cookieHeader = req.headers.get("cookie");
      const authDataFromCookies = parseCookieData<string[]>(cookieHeader, PERMISSIONS_COOKIES_KEY);
      const userPermissions = authDataFromCookies || [];

      // Allow dashboard access and routes with valid permissions
      if (path === ROUTES.DASHBOARD || hasPermissionForRoute(path, userPermissions, getStaticRoutePermissions(), getDynamicRoutePatterns())) {
        return intlMiddleware(req);
      } else {
        // Redirect to dashboard if user lacks permission
        console.log("🔄 Redirecting user without permission to dashboard");
        return NextResponse.redirect(new URL(ROUTES.DASHBOARD, req.url));
      }
    }

    // Handle unauthenticated users
    if (!isPublicRoute) {
      // Redirect to login for protected routes
      console.log("🔄 Redirecting unauthenticated user to login");
      return NextResponse.redirect(new URL(ROUTES.LOGIN, req.url));
    }

    // Allow public routes for unauthenticated users
    return intlMiddleware(req);
  } catch (error) {
    console.error("❌ Error in middleware:", error);
    // On error, allow the request to continue to avoid breaking the app
    return intlMiddleware(req);
  }
}

export const config = {
  matcher: [
    // Match all pathnames except for
    // - API routes (/api/...)
    // - NextAuth API routes (/api/auth/...)
    // - Internal Next.js routes (_next/..., _vercel/...)
    // - Static files (*.ico, *.png, etc.)
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};
