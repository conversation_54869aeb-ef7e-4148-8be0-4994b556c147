/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
"use client";
import React, { useMemo } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

import "../../styles/eventModal.scss";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import EditIcon from "../svgComponents/EditIcon";
import { IGetInterviewsResponse } from "@/interfaces/interviewInterfaces";
import { INTERVIEW_SCHEDULE_ROUND_TYPE, PERMISSION } from "@/constants/commonConstants";
import JoinMeetingIcon from "../svgComponents/JoinMeetingIcon";
import CopyLinkDarkIcon from "../svgComponents/CopyLinkDarkIcon";
import InterviewIcon from "../svgComponents/InterviewIcon";
import { encryptInfo, toastMessageSuccess, toastMessageError, dismissAllToasts } from "@/utils/helper";
import ROUTES from "@/constants/routes";
import { useHasPermission } from "@/utils/permission";
import { useSelector } from "react-redux";
import { AuthState } from "@/redux/slices/authSlice";
import ExternalLinkIcon from "../svgComponents/externalLink";
interface IProps {
  onClose: () => void;
  onEdit: () => void;
  interviewInfo: IGetInterviewsResponse | null;
  attachments: string[];
}

const InterviewDetailModal = ({ onClose, onEdit, interviewInfo, attachments }: IProps) => {
  const start = new Date(interviewInfo?.start!);
  const end = new Date(interviewInfo?.end!);
  const durationMs = end.getTime() - start.getTime();
  const durationMins = Math.round(durationMs / (1000 * 60));

  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);

  const router = useRouter();
  const t = useTranslations();
  const tJob = useTranslations("jobRequirement");

  // function to navigate on pre interview questions screen
  const handleOnNavigate = async () => {
    // Check if skill questions are generated before navigating
    if (!interviewInfo?.skillQuestionsGenerated) {
      dismissAllToasts();
      toastMessageError(t("questions_are_being_generated"));
      return;
    }

    const info = encryptInfo(
      JSON.stringify({
        interviewId: interviewInfo?.id,
        jobApplicationId: interviewInfo?.jobApplicationId,
        interviewType: interviewInfo?.roundType,
        resumeLink: interviewInfo?.resumeLink,
        isEnded: interviewInfo?.isEnded,
        date: interviewInfo?.start,
        time: interviewInfo?.end,
        jobId: interviewInfo?.jobId,
        channelName: interviewInfo?.channelName,
        candidateId: interviewInfo?.candidateId,
        candidateName: interviewInfo?.candidateName,
        interviewerName: interviewInfo?.interviewerName,
        interviewerId: interviewInfo?.interviewerId,
      })
    );

    const encoded = encodeURIComponent(info);

    router.push(`${ROUTES.INTERVIEW.PRE_INTERVIEW_QUESTIONS_OVERVIEW}?info=${encoded}`);
  };

  const hasEditScheduledInterviewPermission = useHasPermission(PERMISSION.SCHEDULE_CONDUCT_INTERVIEWS);
  // const hasViewAllInterviewsPermission = useHasPermission(PERMISSION.VIEW_ALL_SCHEDULED_INTERVIEWS);
  const hasManageCandidateProfilePermission = useHasPermission(PERMISSION.MANAGE_CANDIDATE_PROFILE);

  console.log("hasEditScheduledInterviewPermission", hasEditScheduledInterviewPermission);
  // console.log("hasViewAllInterviewsPermission", hasViewAllInterviewsPermission);

  const isAuthorized = useMemo(() => {
    if (interviewInfo?.interviewerId && authData?.id === interviewInfo?.interviewerId) return true;
    return false;
  }, [authData?.id, interviewInfo?.interviewerId]);

  const isExpired = useMemo(() => {
    if (!interviewInfo || !interviewInfo?.start) return false;
    const currentDate = new Date();
    const date = new Date(interviewInfo?.start);
    return date < currentDate;
  }, [interviewInfo]);

  return (
    <>
      <div className="modal theme-modal show-modal modal-lg interview-details-modal">
        <div className="modal-dialog modal-dialog-centered ">
          <div className="modal-content">
            <div className="modal-header secondary-header">
              <h4 className="m-0">{t("interview_details")}</h4>
              {!interviewInfo?.isEnded ? (
                <Button type="submit" className="clear-btn p-0 primary" onClick={onEdit}>
                  {hasEditScheduledInterviewPermission ? (
                    <>
                      <EditIcon className="me-2" /> {t("edit_details")}
                    </>
                  ) : null}
                </Button>
              ) : null}
              <Button className="modal-close-btn" onClick={onClose}>
                <ModalCloseIcon />
              </Button>
            </div>
            <div className="modal-body">
              {interviewInfo?.isEnded ? null : interviewInfo?.roundType === INTERVIEW_SCHEDULE_ROUND_TYPE[1].value ? (
                <div className="button-align mb-5">
                  {!interviewInfo.isEnded && !isExpired && isAuthorized ? (
                    <Button type="submit" className="primary-btn rounded-md" onClick={handleOnNavigate}>
                      <JoinMeetingIcon className="me-3" />
                      {t("join_meeting")}
                    </Button>
                  ) : null}
                  {!interviewInfo.isEnded && !isExpired && isAuthorized ? (
                    <Button
                      className="dark-outline-btn rounded-md"
                      onClick={() => {
                        const info = encryptInfo(
                          JSON.stringify({
                            interviewId: interviewInfo?.id,
                            jobApplicationId: interviewInfo?.jobApplicationId,
                            channelName: interviewInfo?.channelName,
                            candidateId: interviewInfo?.candidateId,
                            candidateName: interviewInfo?.candidateName,
                            interviewerName: interviewInfo?.interviewerName,
                          })
                        );
                        const encoded = encodeURIComponent(info);

                        navigator.clipboard.writeText(`${process.env.NEXT_PUBLIC_URL}/candidate-join?params=${encoded}`);
                        toastMessageSuccess(t("link_copied"));
                      }}
                    >
                      <CopyLinkDarkIcon className="me-3" />
                      {t("copy_link")}
                    </Button>
                  ) : null}
                </div>
              ) : !interviewInfo?.isEnded && !isExpired && isAuthorized ? (
                <div className="button-align mb-5">
                  <Button type="submit" className="primary-btn rounded-md" onClick={handleOnNavigate}>
                    <InterviewIcon className="me-3" />
                    {t("start_interview")}
                  </Button>
                </div>
              ) : null}
              <div className="row g-4">
                <div className="col-md-6">
                  <p>{tJob("job_title")}</p>
                  <h4>{interviewInfo?.jobTitle}</h4>
                </div>
                <div className="col-md-6">
                  <p>{t("job_id")}</p>
                  <h4>{interviewInfo?.jobUniqueId}</h4>
                </div>
                <div className="col-md-6">
                  <p>{t("interview_title")}</p>
                  <h4>{interviewInfo?.title}</h4>
                </div>
                <div className="col-md-6">
                  <p>{t("candidate")}</p>
                  <h4
                    className={`high-light-text ${hasManageCandidateProfilePermission ? "active-link" : ""}`}
                    onClick={() => {
                      if (hasManageCandidateProfilePermission && interviewInfo?.jobApplicationId) {
                        window.open(`${ROUTES.JOBS.CANDIDATE_PROFILE}/${interviewInfo?.jobApplicationId}`, "_blank");
                      }
                    }}
                  >
                    {interviewInfo?.candidateName}
                    {hasManageCandidateProfilePermission && <ExternalLinkIcon />}
                  </h4>
                </div>
                <div className="col-md-6">
                  <p>{t("candidate_email")}</p>
                  <a href={`mailto:${interviewInfo?.candidateEmail}`}>{interviewInfo?.candidateEmail}</a>
                </div>
                <div className="col-md-6">
                  <p>{t("interviewer")}</p>
                  <h4 className="high-light-text">{interviewInfo?.interviewerName}</h4>
                </div>
                <div className="col-md-6">
                  <p>{t("interview_type")}</p>
                  <h4>{interviewInfo?.roundType === INTERVIEW_SCHEDULE_ROUND_TYPE[0].value ? t("one_on_one") : t("video_call")}</h4>
                </div>
                <div className="col-md-6">
                  <p>{t("interview_date")}</p>
                  <h4>{new Date(interviewInfo?.start!).toLocaleDateString("en-US", { year: "numeric", month: "long", day: "numeric" })}</h4>
                </div>
                <div className="col-md-6">
                  <p>{t("interview_duration")}</p>
                  <h4>
                    {new Date(interviewInfo?.start!).toLocaleTimeString("en-US", {
                      hour: "numeric",
                      minute: "2-digit",
                      hour12: false,
                    })}{" "}
                    -{" "}
                    {new Date(interviewInfo?.end!).toLocaleTimeString("en-US", {
                      hour: "numeric",
                      minute: "2-digit",
                      hour12: false,
                    })}{" "}
                    | {durationMins} {t("minutes")}
                  </h4>
                </div>
                <div className="col-md-6">
                  <p>{t("interview_round_number")}</p>
                  <h4>{interviewInfo?.roundNumber}</h4>
                </div>
              </div>
              <div className="row g-4 mt-1">
                {interviewInfo?.resumeLink && (
                  <div className="col-md-6">
                    <p>{t("candidate_resume_")}</p>
                    <a href={interviewInfo?.resumeLink} target="_blank" className="text-primary color-primary">
                      {t("view_resume")}
                    </a>
                  </div>
                )}
                {attachments?.length > 0 ? (
                  <>
                    <div className="col-md-6">
                      <p>{t("additional_document")}</p>
                      <a href={attachments[0]} target="_blank" className="text-primary color-primary">
                        {t("attachment")} 1
                      </a>
                      {attachments?.length > 1 ? (
                        <a href={attachments[1]} target="_blank" className="text-primary color-primary">
                          , {t("attachment")} 2
                        </a>
                      ) : null}
                      {attachments?.length > 2 ? (
                        <a href={attachments[2]} target="_blank" className="text-primary color-primary">
                          , {t("attachment")} 3
                        </a>
                      ) : null}
                    </div>
                  </>
                ) : null}
                <div className="col-md-6">
                  <p>{t("interview_questions")}</p>
                  <p onClick={handleOnNavigate} className="text-primary color-primary cursor-pointer">
                    {t("view_interview_questions")}
                  </p>
                </div>
                {interviewInfo?.description ? (
                  <div className="col-md-12">
                    <p>{t("additional_notes")}</p>
                    <h5> {interviewInfo?.description}</h5>
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default React.memo(InterviewDetailModal);
