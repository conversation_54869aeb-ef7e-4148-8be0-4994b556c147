"use client";

import React, { useState, useEffect } from "react";

// External libraries
import { useRouter } from "next/navigation";
import Image from "next/image";

// CSS
import style from "@/styles/commonPage.module.scss";
import noImageFound from "../../../../public/assets/images/noImageFound.svg";
import ApplicationsSourcesModal from "@/components/commonModals/ApplicationsSourcesModal";
import { useTranslations } from "next-intl";

// Services and interfaces
import { fetchAllHiredCandidates } from "@/services/CandidatesServices/candidatesApplicationServices";
import { IHiredCandidate } from "@/interfaces/candidatesInterface";
import TableSkeleton from "@/components/views/skeletons/TableSkeleton";
import ROUTES from "@/constants/routes";
import { toastMessageError } from "@/utils/helper";
import { useTranslate } from "@/utils/translationUtils";

function HiredCandidates() {
  const [showApplicationsSourcesModal, setShowApplicationsSourcesModal] = useState(false);
  const [hiredCandidates, setHiredCandidates] = useState<IHiredCandidate[]>([]);
  const [loading, setLoading] = useState(true);
  const t = useTranslations();
  const translate = useTranslate();
  const router = useRouter();

  // Handle candidate name click to navigate to profile
  const handleCandidateClick = (jobApplicationId: number) => {
    router.push(`${ROUTES.JOBS.CANDIDATE_PROFILE}/${jobApplicationId}`);
  };

  useEffect(() => {
    loadHiredCandidates();
  }, []);

  // Fetch hired candidates on component mount
  const loadHiredCandidates = async () => {
    try {
      setLoading(true);
      const response = await fetchAllHiredCandidates();
      console.log("response", response);
      if (response.data.success) {
        setHiredCandidates(response.data.data);
      } else {
        const errorMessage = response.data.message || "failed_to_fetch_hired_candidates";
        console.error("Error fetching hired candidates:", errorMessage);
        toastMessageError(translate(errorMessage));
      }
    } catch (err) {
      toastMessageError(t("error_fetching_hired_candidates"));
      console.error("Error fetching hired candidates:", err);
    } finally {
      setLoading(false);
    }
  };

  // Format date helper function
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return "N/A";
    }
  };

  return (
    <div className="container">
      {/* --- Page Header --- */}
      <div className="common-page-header">
        <div className="common-page-head-section">
          <div className="main-heading">
            <h2>
              Hired <span>{t("candidates")}</span>
            </h2>
          </div>
        </div>
      </div>

      {/* --- Main Layout --- */}
      <div className="common-box">
        <main className="main-content">
          <div className={style.dashboard_page}>
            {/* --- Hired Candidates Table --- */}
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>Candidate Name</th>
                    <th>Job Title</th>
                    <th>Date Hired</th>
                    <th>Job Compatibility</th>
                    <th>Interviewed By</th>
                  </tr>
                </thead>
                {loading ? (
                  <TableSkeleton rows={7} cols={5} colWidths="200,180,120,100,150" />
                ) : (
                  <tbody>
                    {hiredCandidates.length > 0 ? (
                      hiredCandidates.map((candidate, index) => (
                        <tr key={`candidate-${index}`}>
                          <td>
                            <div
                              onClick={() => candidate.jobApplicationId && handleCandidateClick(candidate.jobApplicationId)}
                              className={`color-primary cursor-pointer text-decoration-underline d-inline ${
                                candidate.jobApplicationId ? "" : "disabled"
                              }`}
                            >
                              {candidate.candidateName}
                            </div>
                          </td>
                          <td>{candidate.jobTitle}</td>
                          <td>{formatDate(candidate.hiredDate)}</td>
                          <td>{candidate.overallSuccessProbability ? `${candidate.overallSuccessProbability}%` : "N/A"}</td>
                          <td>
                            <ul className="multi-user-list">
                              {candidate.interviewers.map((interviewer) => (
                                <li key={interviewer.id} title={interviewer.name}>
                                  <Image src={interviewer.image || noImageFound} alt={interviewer.name} width={32} height={32} />
                                </li>
                              ))}
                            </ul>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="text-center p-4">
                          No hired candidates found.
                        </td>
                      </tr>
                    )}
                  </tbody>
                )}
              </table>
            </div>
          </div>
        </main>
      </div>

      {/* --- Modals --- */}
      {showApplicationsSourcesModal && <ApplicationsSourcesModal onCancel={() => setShowApplicationsSourcesModal(false)} />}
    </div>
  );
}

export default HiredCandidates;
