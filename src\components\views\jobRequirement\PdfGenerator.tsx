import React from "react";
import { toast } from "react-hot-toast";
import html2pdf from "html2pdf.js";
import Button from "@/components/formElements/Button";
import DownloadResumeIcon from "@/components/svgComponents/DownloadResumeIcon";
import { http } from "@/utils/http";
type PdfGeneratorProps = {
  content: string;
  fileName: string;
  onLoadingChange?: (isLoading: boolean) => void;
  title?: string;
  subtitle?: string;
  companyLogo?: string;
  watermark?: string;
  theme?: "default" | "professional" | "modern" | "minimal";
  pageBreaks?: boolean;
  footerLogo?: string;
};

const PdfGenerator = ({
  content,
  fileName,
  onLoadingChange,
  title,
  subtitle,
  companyLogo,
  watermark,
  theme = "default",
  pageBreaks = true,
  footerLogo,
}: PdfGeneratorProps) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [footerLogoBase64, setFooter<PERSON>ogoBase64] = React.useState("");

  console.log("footerLogoBase64", footerLogoBase64);

  const convertImageUrlToBase64 = async (url: string): Promise<string> => {
    try {
      const response = await http.get(url, {
        responseType: "arraybuffer",
      });

      const base64 = Buffer.from(response.data, "binary").toString("base64");
      return `data:image/png;base64,${base64}`;
    } catch (error) {
      console.error("Error converting image to base64:", error);
      return ""; // Return empty string if conversion fails
    }
  };
  const getThemeStyles = (theme: string) => {
    const baseStyles = {
      fontFamily: "Arial, sans-serif",
      lineHeight: "1.6",
      color: "#333",
      padding: "15mm 20mm 20mm 20mm",
      boxSizing: "border-box",
      width: "100%",
      minHeight: "100vh",
      position: "relative",
    };

    const themes = {
      default: {
        ...baseStyles,
        fontSize: "11pt",
        backgroundColor: "#ffffff",
      },
      professional: {
        ...baseStyles,
        fontSize: "10pt",
        fontFamily: "Georgia, serif",
        backgroundColor: "#fafafa",
        borderLeft: "4px solid #2563eb",
        paddingLeft: "24mm",
      },
      modern: {
        ...baseStyles,
        fontSize: "11pt",
        fontFamily: "Helvetica, Arial, sans-serif",
        backgroundColor: "#ffffff",
        borderTop: "3px solid #10b981",
      },
      minimal: {
        ...baseStyles,
        fontSize: "12pt",
        fontFamily: "Helvetica, Arial, sans-serif",
        backgroundColor: "#ffffff",
        padding: "20mm",
      },
    };

    return themes[theme as keyof typeof themes] || themes.default;
  };

  const createStyledContent = (content: string) => {
    const themeStyles = getThemeStyles(theme);

    return `
      <div style="${Object.entries(themeStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, "-$1").toLowerCase()}: ${value}`)
        .join("; ")}">
        
        ${
          companyLogo
            ? `
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="${companyLogo}" alt="Company Logo" style="max-height: 40px; max-width: 150px;" />
          </div>
        `
            : ""
        }
        
        ${
          title
            ? `
          <div style="text-align: center; margin-bottom: 15px;">
            <h1 style="
              margin: 0; 
              font-size: ${theme === "minimal" ? "18pt" : "16pt"}; 
              font-weight: bold; 
              color: ${theme === "professional" ? "#1f2937" : theme === "modern" ? "#10b981" : "#2563eb"};
              border-bottom: ${theme === "minimal" ? "none" : "2px solid #e5e7eb"};
              padding-bottom: ${theme === "minimal" ? "0" : "10px"};
            ">
              ${title}
            </h1>
          </div>
        `
            : ""
        }
        
        ${
          subtitle
            ? `
          <div style="text-align: center; margin-bottom: 25px;">
            <p style="
              margin: 0; 
              font-size: 12pt; 
              color: #6b7280; 
              font-style: italic;
            ">
              ${subtitle}
            </p>
          </div>
        `
            : ""
        }
        
        <div style="position: relative; z-index: 1;">
          ${content}
        </div>
        
        ${
          watermark
            ? `
          <div style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 48pt;
            color: rgba(0, 0, 0, 0.05);
            z-index: 0;
            pointer-events: none;
            font-weight: bold;
            white-space: nowrap;
          ">
            ${watermark}
          </div>
        `
            : ""
        }
        
        <div style="
          position: fixed;
          bottom: 10mm;
          right: 20mm;
          font-size: 8pt;
          color: #9ca3af;
          z-index: 2;
        ">
          Generated on ${new Date().toLocaleDateString()}
        </div>
      </div>

      ${
        footerLogoBase64
          ? `
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="${footerLogoBase64}" alt="Footer Logo" style="max-height: 40px; max-width: 150px;" />
          </div>
        `
          : ""
      }
      
      <style>
        @media print {
          ${
            pageBreaks
              ? `
            h1, h2, h3 { 
              page-break-after: avoid; 
              page-break-inside: avoid; 
            }
            
            p, li { 
              page-break-inside: avoid; 
            }
            
            .page-break { 
              page-break-before: always; 
            }
            
            .no-break { 
              page-break-inside: avoid; 
            }
          `
              : ""
          }
          
          table { 
            border-collapse: collapse; 
            width: 100%; 
            margin: 10px 0; 
          }
          
          table, th, td { 
            border: 1px solid #ddd; 
          }
          
          th, td { 
            padding: 8px; 
            text-align: left; 
          }
          
          th { 
            background-color: #f8f9fa; 
            font-weight: bold; 
          }
          
          ul, ol { 
            margin: 10px 0; 
            padding-left: 25px; 
          }
          
          li { 
            margin-bottom: 5px; 
          }
          
          blockquote {
            margin: 15px 0;
            padding: 10px 15px;
            border-left: 4px solid #e5e7eb;
            background-color: #f9fafb;
            font-style: italic;
          }
          
          code {
            background-color: #f3f4f6;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 90%;
          }
          
          pre {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 90%;
            line-height: 1.4;
          }
        }
      </style>
    `;
  };

  const generatePdf = async () => {
    if (!content) {
      toast.error("No content to generate PDF");
      return;
    }

    try {
      setIsLoading(true);
      if (onLoadingChange) onLoadingChange(true);

      // Create a temporary div with the styled content
      const element = document.createElement("div");
      element.innerHTML = createStyledContent(content);

      let tempFooterLogoBase64 = "";
      if (footerLogo) {
        tempFooterLogoBase64 = await convertImageUrlToBase64(footerLogo);
        setFooterLogoBase64(tempFooterLogoBase64);
      }

      // Enhanced PDF options
      const options = {
        filename: `${fileName.replace(/[^a-zA-Z0-9]/g, "_")}_${new Date().toISOString().slice(0, 19).replace(/[-:T]/g, "")}.pdf`,
        image: {
          type: "jpeg",
          quality: 0.98,
        },
        html2canvas: {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: "#ffffff",
          removeContainer: true,
          logging: false,
        },
        jsPDF: {
          unit: "mm",
          format: "a4",
          orientation: "portrait",
          compress: true,
        },
        pagebreak: pageBreaks
          ? {
              mode: ["avoid-all", "css", "legacy"],
              before: ".page-break",
              after: ".page-break-after",
              avoid: ".no-break",
            }
          : undefined,
      };

      // Generate and download PDF
      await html2pdf().set(options).from(element).save();

      toast.success("PDF generated successfully");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF. Please try again.");
    } finally {
      setIsLoading(false);
      if (onLoadingChange) onLoadingChange(false);
    }
  };

  return (
    <Button
      className="clear-btn p-0 ms-3"
      disabled={!content || isLoading}
      onClick={generatePdf}
      title={isLoading ? "Generating PDF..." : "Download PDF"}
    >
      <DownloadResumeIcon />
    </Button>
  );
};

export default PdfGenerator;
