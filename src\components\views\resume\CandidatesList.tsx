/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import React, { useCallback, useEffect, useState } from "react";
import dayjs from "dayjs";
// import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { useHasPermission } from "@/utils/permission";
// import InfiniteScroll from "react-infinite-scroll-component";

import Button from "@/components/formElements/Button";
// import InputWrapper from "@/components/formElements/InputWrapper";
// import Textbox from "@/components/formElements/Textbox";
// import SearchIcon from "@/components/svgComponents/SearchIcon";
import ThreeDotsIcon from "@/components/svgComponents/ThreeDotsIcon";
import CandidateApproveRejectModal from "@/components/commonModals/CandidateApproveRejectModal";
import ArchiveCandidateModal from "@/components/commonModals/ArchiveCandidateModal"; //

// Services
import {
  fetchCandidatesApplications,
  fetchTopCandidatesApplications,
  promoteDemoteCandidate,
} from "@/services/CandidatesServices/candidatesApplicationServices";
import { archiveActiveApplication } from "@/services/CandidatesServices/candidatesApplicationStatusUpdateService";

import { AuthState } from "@/redux/slices/authSlice";
import { DEFAULT_LIMIT, PERMISSION, SEARCH_REGEX, TYPING_PREVENTION_CHARACTERS } from "@/constants/commonConstants";
import { APPLICATION_STATUS } from "@/constants/jobRequirementConstant";

import style from "@/styles/commonPage.module.scss";
import { useTranslations } from "use-intl";
import { CandidateApplication, PromoteDemotePayload, topCandidateApplication } from "@/interfaces/candidatesInterface";
import "react-loading-skeleton/dist/skeleton.css";
import InfiniteScroll from "react-infinite-scroll-component";
import { APPLICATION_UPDATE_STATUS } from "@/constants/screenResumeConstant";
import { toastMessageError, toastMessageSuccess, toTitleCase } from "@/utils/helper";
import TableSkeleton from "../skeletons/TableSkeleton";
import ROUTES from "@/constants/routes";
import { debounce } from "lodash";
import FullPageLoader from "@/components/commonComponent/FullPageLoader";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import SearchIcon from "@/components/svgComponents/SearchIcon";
import { useForm } from "react-hook-form";
import { useTranslate } from "@/utils/translationUtils";

const CandidatesList = ({
  params,
  searchParams,
}: {
  params: Promise<{ jobId: string }>;
  searchParams: Promise<{ title: string; jobUniqueId: string }>;
}) => {
  const { control } = useForm({ mode: "onChange" });
  const userData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const hasArchiveRestoreCandidatesPermission = useHasPermission(PERMISSION.ARCHIVE_RESTORE_CANDIDATES);
  const hasManualResumeScreeningPermission = useHasPermission(PERMISSION.MANUAL_RESUME_SCREENING);
  const hasEditScheduledInterviewsPermission = useHasPermission(PERMISSION.SCHEDULE_CONDUCT_INTERVIEWS);
  const hasAddAdditionalCandidateInfoPermission = useHasPermission(PERMISSION.ADD_ADDITIONAL_CANDIDATE_INFO);
  const hasManageTopCandidatesPermission = useHasPermission(PERMISSION.MANAGE_TOP_CANDIDATES);
  const hasManageCandidateProfilePermission = useHasPermission(PERMISSION.MANAGE_CANDIDATE_PROFILE);
  const hasHireCandidatePermission = useHasPermission(PERMISSION.HIRE_CANDIDATE);
  const hasManageCandidatePermission = hasHireCandidatePermission || hasManageCandidateProfilePermission;

  const router = useRouter();

  const paramsPromise = React.use(params);
  const searchParamsPromise = React.use(searchParams);

  // State for candidates data
  const [candidates, setCandidates] = useState<CandidateApplication[]>([]);
  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);
  const [selectedCandidate, setSelectedCandidate] = useState<CandidateApplication | topCandidateApplication | null>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [showArchiveModal, setShowArchiveModal] = useState(false); //
  const [searchStr, setSearchStr] = useState(""); // <-- make searchStr stateful and settable
  const [topCandidates, setTopCandidates] = useState<topCandidateApplication[]>([]);
  const [disable, setDisable] = useState(false);
  const t = useTranslations();
  const translate = useTranslate();
  const dropdownRefs = React.useRef<{ [key: number]: HTMLUListElement | null }>({});
  // const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  const [loadingFullScreen, setFullScreenLoading] = useState(false);

  useEffect(() => {
    if (!Number(paramsPromise.jobId) || !searchParamsPromise.title) {
      router.push(ROUTES.DASHBOARD);
    }
  }, [paramsPromise.jobId]);

  // const observer = useRef<IntersectionObserver | null>(null);

  const fetchTopCandidates = async () => {
    if (!userData?.orgId || !paramsPromise?.jobId) return;

    setLoading(true);
    try {
      const response = await fetchTopCandidatesApplications(Number(paramsPromise.jobId));
      if (response?.data?.success) {
        setTopCandidates(response.data.data);
      } else {
        setTopCandidates([]);
      }
    } catch {
      setTopCandidates([]);
    } finally {
      setLoading(false);
    }
  };

  const handlePromoteDemoteCandidate = async (candidate: CandidateApplication | topCandidateApplication, action: APPLICATION_UPDATE_STATUS) => {
    setDisable(true);
    setFullScreenLoading(true);
    try {
      const payload = {
        candidateId: candidate.candidateId,
        applicationId: candidate.applicationId,
        action,
      };

      const response = await promoteDemoteCandidate(payload as PromoteDemotePayload);

      if (response?.data?.success) {
        if (action === APPLICATION_UPDATE_STATUS.PROMOTED) {
          // Candidate is currently in the 'other candidates' list
          const fromOther = candidate as CandidateApplication;

          const filteredCandidates = candidates.filter((c) => c.applicationId !== fromOther.applicationId);
          // Remove from other candidates
          setCandidates(filteredCandidates);

          // Add to top candidates
          const promoted: topCandidateApplication = {
            candidateName: fromOther.candidateName,
            candidateEmail: fromOther.candidateEmail,
            currentRound: fromOther.currentRound,
            applicationCreatedTs: fromOther.applicationCreatedTs,
            atsScore: fromOther.atsScore,
            applicationId: fromOther.applicationId,
            applicationRankStatus: APPLICATION_UPDATE_STATUS.PROMOTED,
            candidateId: fromOther.candidateId,
            aiReason: fromOther.aiReason,
            aiDecision: fromOther.aiDecision,
            applicationStatus: fromOther.applicationStatus,
            hiringManagerReason: fromOther.hiringManagerReason,
            applicationUpdatedTs: new Date().toISOString(),
            applicationSource: fromOther.applicationSource || "", // Ensure this is set
            jobId: fromOther.jobId || 0, // replace if you have this info
            isTopApplication: fromOther.isTopApplication || false,
          };

          setTopCandidates([...topCandidates, promoted]);
        } else if (action === APPLICATION_UPDATE_STATUS.DEMOTED) {
          // Candidate is currently in the 'top candidates' list
          const fromTop = candidate as topCandidateApplication;

          // Remove from top candidates
          setTopCandidates((prev) => prev.filter((c) => c.applicationId !== fromTop.applicationId));

          // Add to other candidates
          const demoted: CandidateApplication = {
            candidateId: fromTop.candidateId,
            candidateName: fromTop.candidateName,
            candidateEmail: fromTop.candidateEmail,
            currentRound: fromTop.currentRound,
            applicationId: fromTop.applicationId,
            applicationStatus: fromTop.applicationStatus, // Preserve the original status
            applicationSource: fromTop.applicationSource || "",
            applicationCreatedTs: fromTop.applicationCreatedTs,
            applicationUpdatedTs: new Date().toISOString(),
            isActive: true,
            jobId: fromTop.jobId || 0,
            hiring_manager_id: 0,
            hiringManagerReason: fromTop.hiringManagerReason || "",
            applicationRankStatus: APPLICATION_UPDATE_STATUS.DEMOTED,
            atsScore: fromTop.atsScore,
            aiReason: fromTop.aiReason,
            aiDecision: fromTop.aiDecision,
          };

          setCandidates([...candidates, demoted]);
        }
      } else {
        toastMessageError(translate(response?.data?.message));
      }
    } catch {
    } finally {
      setActiveDropdown(null);
      setDisable(false);
      setFullScreenLoading(false);
    }
  };

  const fetchMoreCandidatesApplications = useCallback(async (currentOffset = 0, reset = false, searchStr: string = "") => {
    if (!userData?.orgId) return;
    setLoader(true);
    try {
      const response = await fetchCandidatesApplications({
        page: currentOffset,
        limit: DEFAULT_LIMIT,
        searchStr: searchStr,
        isActive: true,
        jobId: Number(paramsPromise.jobId),
      });

      if (response?.data?.success) {
        const newCandidates: CandidateApplication[] = response.data.data;

        // Use a function form of setCandidates to ensure we have the latest state
        setCandidates((prevCandidates) => {
          return reset ? newCandidates : [...prevCandidates, ...newCandidates];
        });

        if (newCandidates.length < DEFAULT_LIMIT) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }
        setOffset(currentOffset + newCandidates.length);
      } else {
        setHasMore(false);
      }
    } catch {
      setHasMore(false);
    } finally {
      setLoader(false);
    }
  }, []);

  const loadMoreCandidates = () => {
    if (!loader && hasMore) fetchMoreCandidatesApplications(offset, false, searchStr);
  };

  // const lastElementRef = useCallback(
  //   (node: HTMLElement | null) => {
  //     if (loading) return;
  //     if (observer.current) observer.current.disconnect();
  //     observer.current = new IntersectionObserver((entries) => {
  //       if (entries[0].isIntersecting && hasMore) loadMoreCandidates();
  //     });
  //     if (node) observer.current.observe(node);
  //   },
  //   [loading, hasMore, offset]
  // );

  useEffect(() => {
    if (userData?.id && userData?.orgId) fetchTopCandidates();
  }, [userData?.id, userData?.orgId]);

  const debouncedHandleSearchInputChange = useCallback(
    debounce((value: string) => {
      if (userData?.id && userData?.orgId) {
        const trimmed = value.trim();
        // If input is empty, fetch default list
        if (trimmed.length === 0) {
          fetchMoreCandidatesApplications(0, true, "");
        } else {
          // If input is only special characters, show no data found (skip API call)
          if (SEARCH_REGEX.test(trimmed)) {
            setCandidates([]);
            setHasMore(false);
          } else {
            // Clear candidates immediately when starting search to show skeleton
            setCandidates([]);
            fetchMoreCandidatesApplications(0, true, trimmed);
          }
        }
      }
    }, 300),
    [userData?.id, userData?.orgId, fetchMoreCandidatesApplications]
  );
  useEffect(() => {
    debouncedHandleSearchInputChange(searchStr);
    return () => {
      debouncedHandleSearchInputChange.cancel();
    };
  }, [searchStr, debouncedHandleSearchInputChange]);

  const handleArchiveCandidate = (candidate: CandidateApplication | topCandidateApplication) => {
    setSelectedCandidate(candidate);
    setShowArchiveModal(true);
    setActiveDropdown(null);
  };

  const handleReviewCandidate = (candidate: CandidateApplication | topCandidateApplication) => {
    setSelectedCandidate(candidate);
    setShowReviewModal(true);
    setActiveDropdown(null);
  };

  const onCancelReviewModal = () => {
    setShowReviewModal(false);
    setSelectedCandidate(null);
  };

  const onSubmitArchiveReason = async (reason: string) => {
    if (!selectedCandidate) return;
    try {
      const response = await archiveActiveApplication(selectedCandidate.applicationId, false, reason);
      if (response && response.data && response.data.success) {
        toastMessageSuccess(translate("candidate_archive_successfully"));

        if (selectedCandidate.isTopApplication) {
          setTopCandidates((prev) => prev.filter((c) => c.applicationId !== selectedCandidate.applicationId));
        } else {
          setCandidates((prev) => prev.filter((c) => c.applicationId !== selectedCandidate.applicationId));
        }
        setShowArchiveModal(false);
        setSelectedCandidate(null);
      } else {
        toastMessageError(translate("failed_to_archive_candidate"));
      }
      setShowArchiveModal(false);
      setSelectedCandidate(null);
    } catch (error) {
      console.error("Error archiving candidate:", error);
    }
  };

  const handleStatusChangeSuccess = (candidate: CandidateApplication | topCandidateApplication, status: string) => {
    if (candidate?.isTopApplication) {
      if (status === APPLICATION_STATUS.REJECTED) {
        setTopCandidates((prev) => prev.filter((c) => c.applicationId !== candidate.applicationId));
      } else {
        setTopCandidates((prev) => prev.map((c) => (c.applicationId === candidate.applicationId ? { ...c, applicationStatus: status } : c)));
      }
    } else {
      setCandidates((prev) => prev.map((c) => (c.applicationId === candidate.applicationId ? { ...c, applicationStatus: status } : c)));
    }
    setShowReviewModal(false);
    setSelectedCandidate(null);
  };

  const handleCandidateClick = (jobApplicationId: number) => {
    router.push(`${ROUTES.JOBS.CANDIDATE_PROFILE}/${jobApplicationId}`);
  };
  // Close dropdown when clicking outside
  useEffect(() => {
    let clickedOnDropdownToggle = false;

    const handleMouseDown = (event: MouseEvent) => {
      // Check if the click is on a dropdown toggle button or its children (like the SVG)
      const targetElement = event.target as Element;
      clickedOnDropdownToggle = targetElement.closest(".clear-btn") !== null || targetElement.tagName === "svg" || targetElement.tagName === "path";

      // If clicking outside the dropdown and not on a toggle button, close it
      if (
        activeDropdown &&
        dropdownRefs.current[activeDropdown] &&
        !dropdownRefs.current[activeDropdown]?.contains(targetElement) &&
        !clickedOnDropdownToggle &&
        !targetElement.closest(".applications-sources-modal")
      ) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleMouseDown);

    return () => {
      document.removeEventListener("mousedown", handleMouseDown);
    };
  }, [activeDropdown]);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (TYPING_PREVENTION_CHARACTERS.includes(event.key)) {
      event.preventDefault();
    }
  };

  return (
    <>
      {loadingFullScreen && <FullPageLoader />}

      <section className={`${style.resume_page} ${style.candidates_list_page}`}>
        <div className="container">
          <div className="common-page-header">
            <div className="common-page-head-section">
              <div className="main-heading">
                <h2>
                  {/* <BackArrowIcon onClick={() => router.back()} /> */}
                  {t("candidates_for")} <span>{searchParamsPromise.title}</span>
                </h2>
                <div className="right-action">
                  {hasManualResumeScreeningPermission && (
                    <Button
                      className="primary-btn rounded-md button-sm"
                      onClick={() =>
                        router.push(
                          `${ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD}/${paramsPromise.jobId}?title=${searchParamsPromise.title}&jobUniqueId=${searchParamsPromise.jobUniqueId}`
                        )
                      }
                    >
                      {t("add_candidate")}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className={style.candidates_list_section}>
            <div className={style.section_name}>
              <h3> {t("top_ten_candidates")} </h3>
              <p> {t("based_on_interview_date")} </p>
            </div>
            <div className={`table-responsive min-table ${topCandidates.length < 3 ? "min-data" : ""}`}>
              <table className="table overflow-auto mb-0">
                <thead>
                  <tr>
                    <th style={{ width: "16%" }}> {t("candidate_name")} </th>
                    <th style={{ width: "16%" }}> {t("candidate_email")} </th>
                    <th style={{ width: "16%" }} className="text-center">
                      {" "}
                      {t("current_round")}{" "}
                    </th>
                    <th style={{ width: "16%" }}> {t("date_submitted")} </th>
                    <th style={{ width: "16%" }} className="text-center">
                      {t("ats_score")} (%)
                    </th>
                    {/* <th> {t("lined_up_for")} </th> */}
                    <th style={{ width: "16%" }} className="text-center">
                      {t("candidates_analysis")}
                    </th>

                    <th style={{ width: "16%" }} className="text-center">
                      {" "}
                      {t("actions")}{" "}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {topCandidates.length > 0 ? (
                    topCandidates.map((candidate, index) => {
                      const isPromoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.PROMOTED;
                      const isDemoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.DEMOTED;
                      const dotClass = isPromoted ? "green-dot" : isDemoted ? "red-dot" : "";
                      const statusClass =
                        candidate.applicationStatus === APPLICATION_STATUS.APPROVED
                          ? "color-success"
                          : candidate.applicationStatus === APPLICATION_STATUS.REJECTED
                            ? "color-danger"
                            : "color-dark";
                      const isHired = candidate.applicationStatus === APPLICATION_STATUS.HIRED;

                      return (
                        <tr key={candidate.candidateId}>
                          <td style={{ width: "16%" }}>
                            <div
                              onClick={hasManageCandidatePermission ? () => handleCandidateClick(candidate.applicationId) : undefined}
                              className={`color-primary ${hasManageCandidatePermission ? "cursor-pointer" : ""} ${dotClass} d-inline`}
                            >
                              {index + 1}. <span className="text-decoration-underline">{toTitleCase(candidate.candidateName)}</span>
                            </div>
                          </td>
                          <td style={{ width: "16%" }}>{candidate.candidateEmail}</td>
                          <td style={{ width: "16%" }} className="text-center">
                            {candidate.currentRound}
                          </td>
                          <td style={{ width: "16%" }}>
                            {candidate.applicationCreatedTs ? dayjs(candidate.applicationCreatedTs).format("MMM D, YYYY") : "Not Available"}
                          </td>
                          <td style={{ width: "16%" }} className="text-center">
                            {candidate.atsScore}
                          </td>
                          <td style={{ width: "16%", textAlign: "center" }} className={statusClass}>
                            {candidate.applicationStatus}
                          </td>

                          {/* <td>{candidate.applicationRankStatus}</td> */}
                          <td style={{ width: "16%" }} align="center" className="position-relative">
                            <div>
                              {!isHired ? (
                                <Button
                                  className="clear-btn p-0"
                                  data-candidate-id={candidate.candidateId}
                                  onClick={(e) => {
                                    e.stopPropagation(); // Prevent triggering parent handlers
                                    e.preventDefault();

                                    console.log("Button clicked, currentDropdown:", activeDropdown, "candidateId:", candidate.candidateId);
                                    setActiveDropdown((prev) => (prev === candidate.candidateId ? null : candidate.candidateId));
                                  }}
                                >
                                  <ThreeDotsIcon />
                                </Button>
                              ) : (
                                <>-</>
                              )}
                              {activeDropdown === candidate.candidateId ? (
                                <ul
                                  className="custom-dropdown"
                                  ref={(element) => {
                                    if (element) {
                                      dropdownRefs.current[candidate.candidateId] = element;
                                    }
                                  }}
                                >
                                  {candidate.applicationStatus === APPLICATION_STATUS.APPROVED && hasEditScheduledInterviewsPermission && (
                                    <li
                                      onClick={() =>
                                        router.push(
                                          `${ROUTES.INTERVIEW.SCHEDULE_INTERVIEW}?applicationId=${candidate.applicationId}&candidateName=${encodeURIComponent(candidate.candidateName)}&title=${encodeURIComponent(searchParamsPromise.title)}&jobId=${paramsPromise.jobId}&jobUniqueId=${encodeURIComponent(searchParamsPromise.jobUniqueId)}`
                                        )
                                      }
                                    >
                                      {t("schedule_interview")}{" "}
                                    </li>
                                  )}
                                  {hasAddAdditionalCandidateInfoPermission && (
                                    <li onClick={() => router.push(`${ROUTES.INTERVIEW.ADD_CANDIDATE_INFO}/${candidate.applicationId}`)}>
                                      {t("add_candidates_info")}{" "}
                                    </li>
                                  )}
                                  {[APPLICATION_STATUS.PENDING, APPLICATION_STATUS.ON_HOLD].includes(candidate.applicationStatus) && (
                                    <li onClick={() => handleReviewCandidate(candidate)}>{t("analyze_candidate_resume")}</li>
                                  )}
                                  {hasManageTopCandidatesPermission && !isHired && (
                                    <li
                                      onClick={
                                        !disable
                                          ? () =>
                                              handlePromoteDemoteCandidate(
                                                candidate as unknown as CandidateApplication,
                                                APPLICATION_UPDATE_STATUS.DEMOTED
                                              )
                                          : undefined
                                      }
                                    >
                                      {t("demote_candidate")}
                                    </li>
                                  )}
                                  {hasArchiveRestoreCandidatesPermission && !isHired && (
                                    <li onClick={() => handleArchiveCandidate(candidate)}>{t("archive_candidate")}</li>
                                  )}
                                </ul>
                              ) : null}
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  ) : !loading ? (
                    <tr className={topCandidates.length % 2 === 0 ? "bg-light-blue" : "bg-white"}>
                      <td colSpan={6} className="text-center">
                        {t(topCandidates.length ? "no_more_candidates_to_fetch" : "no_candidates_found")}
                      </td>
                    </tr>
                  ) : null}
                </tbody>
                {loading && <TableSkeleton rows={3} cols={7} colWidths="120,80,100,24,24,24" />}
              </table>
            </div>
          </div>

          {/* Only show Other Candidates section if there are candidates */}
          {(candidates.length > 0 || searchStr !== undefined) && (
            <div className={style.candidates_list_section}>
              <div className={`${style.section_name} d-flex align-items-center justify-content-between`}>
                <div>
                  <h3>{t("other_candidates")}</h3>
                  <p>{t("rancked_by_resume")}</p>
                </div>
                <div className="right-action w-25">
                  <InputWrapper className="mb-0 w-100 search-input">
                    <div className="icon-align right">
                      <Textbox
                        className="form-control w-100"
                        control={control}
                        name="search"
                        type="text"
                        placeholder={t("search_using_name")}
                        value={searchStr}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          const value = e.target.value;
                          // Allow empty string (for clearing), but ignore whitespace-only
                          if (value === "" || value.trim().length > 0) {
                            setSearchStr(value);
                          }
                        }}
                        onKeyDown={handleKeyDown}
                        onPaste={(e) => {
                          const pastedText = e.clipboardData.getData("text");
                          if (TYPING_PREVENTION_CHARACTERS.some((char) => pastedText.includes(char))) {
                            e.preventDefault();
                          }
                        }}
                      >
                        <InputWrapper.Icon>
                          <SearchIcon />
                        </InputWrapper.Icon>
                      </Textbox>
                    </div>
                  </InputWrapper>
                </div>
              </div>
              <div className={`table-responsive min-table ${candidates.length < 3 ? "min-data" : ""}`} id="scrollableCandidatesTableDiv">
                <InfiniteScroll
                  dataLength={candidates.length}
                  next={() => loadMoreCandidates()}
                  hasMore={hasMore}
                  height={candidates.length > 0 ? window.innerHeight - 300 : "auto"}
                  style={{ minHeight: candidates.length > 0 ? undefined : "auto" }}
                  loader={
                    loader && (
                      <>
                        <TableSkeleton rows={3} cols={7} colWidths="120,80,100,24,24,24,24" />
                      </>
                    )
                  }
                  endMessage={
                    !loader && candidates.length ? (
                      <p
                        style={{
                          textAlign: "center",
                          backgroundColor: candidates.length % 2 === 0 ? "#436eb60f" : "#ffffff",
                          padding: "10px",
                          fontSize: "14px",
                        }}
                      >
                        {t("no_more_candidates_to_fetch")}
                      </p>
                    ) : null
                  }
                >
                  <table className="table overflow-auto mb-0">
                    <thead>
                      <tr>
                        <th style={{ width: "16%" }}>{t("candidate_name")}</th>
                        <th style={{ width: "16%" }}>{t("candidate_email")}</th>
                        <th style={{ width: "16%" }} className="text-center">
                          {t("current_round")}
                        </th>
                        <th style={{ width: "16%" }}>{t("date_submitted")}</th>
                        {/* <th>{t("source")}</th> */}
                        <th style={{ width: "16%" }} className="text-center">
                          {t("ats_score")} (%)
                        </th>

                        <th style={{ width: "16%" }} className="text-center">
                          {t("candidates_analysis")}
                        </th>
                        <th style={{ width: "16%" }} className="text-center">
                          {t("actions")}
                        </th>
                      </tr>
                    </thead>
                    {candidates.length > 0 ? (
                      <tbody id="scrollableCandidatesTableBody">
                        {candidates.map((candidate, index) => {
                          const statusClass =
                            candidate.applicationStatus === APPLICATION_STATUS.APPROVED
                              ? "color-success text-center"
                              : candidate.applicationStatus === APPLICATION_STATUS.REJECTED
                                ? "color-danger text-center"
                                : "color-dark text-center";

                          const isPromoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.PROMOTED;
                          const isDemoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.DEMOTED;
                          const dotClass = isPromoted ? "green-dot" : isDemoted ? "red-dot" : "";
                          const isHired = candidate.applicationStatus === APPLICATION_STATUS.HIRED;

                          return (
                            <tr
                              key={candidate.applicationId}
                              // ref={lastElementRef}
                            >
                              <td>
                                <div
                                  onClick={hasManageCandidatePermission ? () => handleCandidateClick(candidate.applicationId) : undefined}
                                  className={`color-primary ${hasManageCandidatePermission ? "cursor-pointer" : ""} ${dotClass} d-inline`}
                                >
                                  {index + 1}.{" "}
                                  <span className="text-decoration-underline"> {toTitleCase(candidate.candidateName) || "Candidate Name"}</span>
                                </div>
                              </td>
                              <td>{candidate.candidateEmail}</td>
                              <td className="text-center">{candidate.currentRound}</td>
                              <td>
                                {candidate.applicationCreatedTs ? dayjs(candidate.applicationCreatedTs).format("MMM D, YYYY") : "Not Available"}
                              </td>
                              <td className="text-center">{candidate.atsScore ?? "N/A"}</td>
                              <td className={statusClass}>{candidate.applicationStatus}</td>
                              <td align="center" className="position-relative">
                                <div>
                                  {!isHired ? (
                                    <Button
                                      className="clear-btn p-0"
                                      data-candidate-id={candidate.candidateId}
                                      onClick={(e) => {
                                        e.stopPropagation(); // Prevent triggering parent handlers
                                        e.preventDefault();

                                        console.log("Button clicked, currentDropdown:", activeDropdown, "candidateId:", candidate.candidateId);

                                        // Force a small timeout to avoid race conditions with the outside click handler
                                        setActiveDropdown((prev) => (prev === candidate.candidateId ? null : candidate.candidateId));
                                      }}
                                    >
                                      <ThreeDotsIcon />
                                    </Button>
                                  ) : (
                                    <>-</>
                                  )}

                                  {activeDropdown === candidate.candidateId && (
                                    <ul
                                      className="custom-dropdown"
                                      ref={(element) => {
                                        if (element) {
                                          dropdownRefs.current[candidate.candidateId] = element;
                                        }
                                      }}
                                    >
                                      {candidate.applicationStatus === APPLICATION_STATUS.APPROVED && hasEditScheduledInterviewsPermission && (
                                        <li
                                          onClick={() =>
                                            router.push(
                                              `${ROUTES.INTERVIEW.SCHEDULE_INTERVIEW}?applicationId=${candidate.applicationId}&candidateName=${encodeURIComponent(candidate.candidateName)}&title=${encodeURIComponent(searchParamsPromise.title)}&jobId=${paramsPromise.jobId}&jobUniqueId=${encodeURIComponent(searchParamsPromise.jobUniqueId)}`
                                            )
                                          }
                                        >
                                          {t("schedule_interview")}
                                        </li>
                                      )}
                                      {hasAddAdditionalCandidateInfoPermission && (
                                        <li onClick={() => router.push(`${ROUTES.INTERVIEW.ADD_CANDIDATE_INFO}/${candidate.applicationId}`)}>
                                          {t("add_candidates_info")}
                                        </li>
                                      )}
                                      {[APPLICATION_STATUS.PENDING, APPLICATION_STATUS.ON_HOLD].includes(candidate.applicationStatus) && (
                                        <li onClick={() => handleReviewCandidate(candidate)}>{t("analyze_candidate_resume")}</li>
                                      )}
                                      {/* <li>{t("analyze_candidate_resume")}</li> */}
                                      {candidate.applicationStatus !== APPLICATION_STATUS.REJECTED &&
                                        !isHired &&
                                        hasManageTopCandidatesPermission && (
                                          <li
                                            onClick={
                                              !disable ? () => handlePromoteDemoteCandidate(candidate, APPLICATION_UPDATE_STATUS.PROMOTED) : undefined
                                            }
                                            style={disable ? { pointerEvents: "none", opacity: 0.5 } : {}}
                                          >
                                            {t("promote_candidate")}
                                          </li>
                                        )}
                                      {hasArchiveRestoreCandidatesPermission && !isHired && (
                                        <li onClick={() => handleArchiveCandidate(candidate)}>{t("archive_candidate")}</li>
                                      )}
                                    </ul>
                                  )}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    ) : (
                      !loader && (
                        <tbody>
                          <tr>
                            <td colSpan={6} style={{ textAlign: "center" }}>
                              {t("no_candidates_found")}
                            </td>
                          </tr>
                        </tbody>
                      )
                    )}
                    {loader && <TableSkeleton rows={3} cols={7} colWidths="120,80,100,24,24,24,24" />}
                  </table>
                </InfiniteScroll>
              </div>
            </div>
          )}
        </div>
      </section>

      {showReviewModal && selectedCandidate && (
        <CandidateApproveRejectModal onClickCancel={onCancelReviewModal} onSuccess={handleStatusChangeSuccess} candidate={selectedCandidate} />
      )}

      {showArchiveModal && selectedCandidate && (
        <ArchiveCandidateModal
          onClickCancel={() => setShowArchiveModal(false)}
          applicationId={selectedCandidate.applicationId}
          jobId={Number(paramsPromise.jobId)}
          onSuccess={(reason) => onSubmitArchiveReason(reason)}
        />
      )}
    </>
  );
};

export default CandidatesList;
