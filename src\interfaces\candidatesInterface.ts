export interface topCandidateApplication {
  candidateName: string;
  candidateEmail: string;
  applicationCreatedTs: string;
  atsScore: number;
  applicationId: number;
  applicationRankStatus: string;
  applicationUpdatedTs: string;
  applicationSource: string;
  candidateId: number;
  aiReason: string;
  aiDecision: string;
  applicationStatus: string;
  hiringManagerReason: string;
  jobId: number;
  isTopApplication?: boolean;
}

export interface CandidateApplication extends topCandidateApplication {
  isActive: boolean | number;
  hiring_manager_id: number;
}

export interface PromoteDemotePayload {
  candidateId: number;
  applicationId: number;
  action: "Promoted" | "Demoted";
}

export interface CandidateProfileResponse {
  candidateName: string;
  jobTitle: string;
  jobId: number;
  status: string;
  resumeLink: string;
  hiringManagerId: number;
  interviewerName: string;
  interviewerImage: string;
  department: string;
  imageUrl: string;
  roundNumber: number;
  candidateEmail: string;
  jobApplicationId: number;
  isFinalAssessmentGenerated: boolean;
  isFinalSummaryGenerated: boolean;
  assessmentId: number | null;
}

export interface AdditionalInfoPayload {
  applicationId: string;
  description: string;
  images: string | null;
}

export interface JobApplicationStatusPayload {
  status: "Hired" | "Final-Reject";
}

export interface ICandidateInterviewHistory {
  roundNumber: number;
  interviewerId: number;
  interviewerName: string;
  interviewerImage: string;
  hardSkillMarks: number;
  interviewSummary: Record<string, string[]>;
  skillScores: Record<string, number>;
  interviewerPerformanceAiAnalysis: { highlights: string[] };
  endTime: string;
}

export interface ISkillScore {
  skillMarks: number;
  skillName: string;
}

export interface IRecommendation {
  title: string;
  description: string;
}

export interface IDevelopmentRecommendations {
  recommendations: IRecommendation[];
}

export interface IFinalAssessment {
  jobApplicationId: number;
  developmentRecommendations: IDevelopmentRecommendations | null;
  skillSummary: Record<string, string[]> | null;
  overallSuccessProbability: number | null;
  behaviouralScores: Record<string, number> | null;
}

export interface ISkillSpecificAssessment {
  careerBasedSkillsScore: number;
  skillsScores: {
    skill_name: string;
    skill_marks: number;
    strengths: { strengths: string[] } | null;
    potentials_gaps: { potentialGaps: string[] } | null;
    probability_of_success_in_this_skill: { probabilityOfSuccessInSkill: number } | null;
  }[];
}

/**
 * Chart data structure for bar chart visualization
 * Used for displaying skill scores in bar chart format
 */
export interface BarChartData {
  labels: string[];
  datasets: Array<{
    data: number[];
    backgroundColor: string[];
    borderRadius: number;
    borderSkipped: boolean;
    barPercentage: number;
  }>;
}

/**
 * Props interface for the CandidateProfile component
 * Defines the expected props structure for the component
 */
export interface CandidateProfileProps {
  /** Promise containing the candidate ID parameter from the URL */
  params: Promise<{ jobApplicationId: string }>;
}

/**
 * Chart data structure for radar chart visualization
 * Used for displaying behavioral assessment metrics in radar chart format
 */
export interface RadarChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    fill: boolean;
    backgroundColor: string;
    borderColor: string;
    pointBackgroundColor: string;
    pointBorderColor: string;
    pointHoverBackgroundColor: string;
    pointHoverBorderColor: string;
  }>;
}

/**
 * Parameters for fetching candidates with applications
 */
export interface FetchCandidatesParams {
  /** Offset for pagination (page number) */
  page: number;
  /** Maximum number of results per page */
  limit: number;
  /** Search string for filtering candidates by name */
  searchStr?: string;
  /** Filter flag: true for active candidates, false for archived */
  isActive: boolean;
  /** Optional job ID filter to get candidates for specific job */
  jobId?: number;
}

/**
 * Interface for interviewer details in hired candidates
 */
export interface IHiredCandidateInterviewer {
  /** Unique identifier for the interviewer */
  id: number;
  /** Full name of the interviewer */
  name: string;
  /** Email of the interviewer */
  email: string;
  /** Profile image URL of the interviewer */
  image: string;
  /** Round number of the interview */
  roundNumber: number;
}

/**
 * Interface for hired candidate data
 */
export interface IHiredCandidate {
  /** Full name of the hired candidate */
  candidateName: string;
  /** Job title/position for which candidate was hired */
  jobTitle: string;
  /** Job application ID for navigation to candidate profile */
  jobApplicationId?: number;
  /** Date when the candidate was hired */
  hiredDate?: string;
  /** Job compatibility percentage */
  overallSuccessProbability?: number;
  /** List of interviewers who interviewed this candidate */
  interviewers: IHiredCandidateInterviewer[];
}

/**
 * API response interface for hired candidates
 */
export interface IHiredCandidatesResponse {
  /** Error information */
  error: null;
  /** Response data */
  data: {
    /** Success flag */
    success: boolean;
    /** Response message */
    message: string;
    /** Response data containing hired candidates */
    data: IHiredCandidate[];
    /** HTTP status code */
    code: number;
  };
}
