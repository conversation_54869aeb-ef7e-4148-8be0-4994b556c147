/* eslint-disable @typescript-eslint/no-empty-object-type */
import { ICurrentPlan } from "@/redux/slices/authSlice";

export interface ILogin {
  email: string;
  password: string;
}

export interface IVerifyOTP {
  otp: string;
  email: string;
}

export interface IResendOTP extends IForgotPassword {}

export interface IForgotPassword {
  email: string;
}

export interface IResetPassword extends ILogin {
  otp: string;
}

export interface Role {
  roleId: number;
  roleName: string;
  roleIsActive: number;
}

export interface IDepartment {
  departmentId: number;
  departmentName: string;
  departmentIsActive: number;
}

export interface Permission {
  permissionId: string;
  permissionName: string;
  permissionSlug: string;
}

export interface IUserData {
  id: number;
  account_type: string;
  email: string;
  isVerified: boolean;
  sms_notification: boolean;
  allow_notification: boolean;
  is_deleted: boolean;
  image: string | null;
  orgId: number;
  departmentId: number;
  organizationName: string;
  organizationCode: string;
  createdTs: string;
  first_name: string;
  last_name: string;
}

export interface ILoginResponse {
  authData: {
    userData: IUserData;
    role: Role;
    department: IDepartment;
    permissions: Permission[];
    currentPlan: ICurrentPlan | null;
  };
  token: string;
}

export interface UserPermissionsResponse {
  rolePermissions: Permission[];
}

/**
 * Interface for current subscription plan data
 */
