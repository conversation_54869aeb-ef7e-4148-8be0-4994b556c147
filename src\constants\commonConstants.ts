import { ExtendedFormValues } from "@/types/types";

export const ACCESS_TOKEN_KEY = "__ATK__";

export const EMAIL_REGEX = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;

export const PASSWORD_REGEX = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,16}$/;
export const NAME_REGEX = /^[a-zA-Z0-9\s.'-]+$/;

export const MAX_IMAGE_SIZE = 5242880;

export const ScheduleInterviewFormSubmissionType = {
  SCHEDULE: "schedule",
  UPDATE: "update",
};

export const AIDecisionForNextRound = {
  APPROVED: "Approved",
  REJECTED: "Rejected",
};

export const SOCKET_ROUTES = {
  CONDUCT_INTERVIEW: "/conduct-interview",
  CANDIDATE_CONDUCT_INTERVIEW: "/candidate-conduct-interview",
};

export const S3_PATHS = {
  PROFILE_IMAGE: "profile-images/:path",
  CONDUCT_INTERVIEW: "job-interviews",
};

export const FOLLOW_UP_TYPE = {
  QUESTION: "question",
  SKILL: "skill",
};

export const INTERVIEW_QUESTION_TYPE = {
  FOLLOW_UP: "follow_up",
};

/**
 * Permission Constants
 */
export const PERMISSION = {
  CREATE_OR_EDIT_JOB_POST: "create-or-edit-job-post",
  SCHEDULE_CONDUCT_INTERVIEWS: "schedule-conduct-interviews",
  VIEW_HIRED_CANDIDATES: "view-hired-candidates",
  ARCHIVE_RESTORE_CANDIDATES: "archive-restore-candidates",
  ARCHIVE_RESTORE_JOB_POSTS: "archive-restore-job-posts",
  MANUAL_RESUME_SCREENING: "manual-resume-screening",
  ADD_ADDITIONAL_CANDIDATE_INFO: "add-additional-candidate-info",
  MANAGE_TOP_CANDIDATES: "manage-top-candidates",
  MANAGE_PRE_INTERVIEW_QUESTIONS: "manage-pre-interview-questions",
  HIRE_CANDIDATE: "hire-candidate",
  CREATE_NEW_ROLE: "create-new-role",
  MANAGE_USER_PERMISSIONS: "manage-user-permissions",
  CREATE_NEW_DEPARTMENT: "create-new-department",
  VIEW_SUBSCRIPTION_PLAN: "view-subscription-plan",
  MANAGE_SUBSCRIPTIONS: "manage-subscriptions",
  VIEW_AUDIT_LOGS_UPCOMING: "view-audit-logs-upcoming",
  VIEW_ALL_SCHEDULED_INTERVIEWS: "view-all-scheduled-interviews",
  ADD_EMPLOYEE: "add-employee",
  MANAGE_CANDIDATE_PROFILE: "manage-candidate-profile",
};

/**
 * Plan Configuration Constants
 */
export const PLAN_CONFIG = {
  free: {
    maxCandidates: 1,
    showAddButton: false,
    planDisplayName: "Free",
  },
  pro: {
    maxCandidates: 1,
    showAddButton: false,
    planDisplayName: "Pro",
  },
  growth: {
    maxCandidates: 5,
    showAddButton: true,
    planDisplayName: "Growth",
  },
  enterprise: {
    maxCandidates: 5,
    showAddButton: true,
    planDisplayName: "Enterprise",
  },
} as const;

/**
 * Plan Configuration Type
 */
export type PlanConfigType = (typeof PLAN_CONFIG)[keyof typeof PLAN_CONFIG];

/**
 * Plan Name Constants
 */
export const PLAN_NAMES = {
  FREE: "Free",
  PRO: "Pro",
  GROWTH: "Growth",
  ENTERPRISE: "Enterprise",
} as const;

/**
 * Skill Constants
 */
export const SKILL_CONSTANTS = {
  REQUIRED_ROLE_SKILLS: 10,
  REQUIRED_CULTURE_SKILLS: 5,
};
export const commonConstants = {
  finalAssessmentId: "finalAssessmentId",
  token: "token",
  isShared: "isShared",
  isSubmitted: "isSubmitted",
  jobId: "jobId",
  jobApplicationId: "jobApplicationId",
};

export const QuestionType = {
  MCQ: "mcq",
  TRUE_FALSE: "true_false",
};

// Constants for option IDs
export const OPTION_ID = {
  A: "A",
  B: "B",
  C: "C",
  D: "D",
  TRUE: "true",
  FALSE: "false",
} as const;

// Constants for question types
export const QUESTION_TYPE = {
  MCQ: "mcq" as const,
  TRUE_FALSE: "true_false" as const,
};

// Constants for default options
export const DEFAULT_MCQ_OPTIONS = [
  { id: OPTION_ID.A, text: "" },
  { id: OPTION_ID.B, text: "" },
  { id: OPTION_ID.C, text: "" },
  { id: OPTION_ID.D, text: "" },
];

export const DEFAULT_TRUE_FALSE_OPTIONS = [
  { id: OPTION_ID.TRUE, text: "True" },
  { id: OPTION_ID.FALSE, text: "False" },
];

export const INTERVIEW_SCHEDULE_ROUND_TYPE = [
  {
    label: "In-Person",
    value: "One-On-One",
  },
  {
    label: "Online Interview",
    value: "Video Call",
  },
];

/**
 * Interview Question Types
 */
export const QUESTION_TYPES = {
  ROLE_SPECIFIC: "role_specific",
  CULTURE_SPECIFIC: "culture_specific",
  CAREER_BASED: "career_based",
} as const;

export type QuestionType = (typeof QUESTION_TYPES)[keyof typeof QUESTION_TYPES];
/**
 * Empty Content Patterns
 */
export const EMPTY_CONTENT_PATTERNS = ["<p><br></p>", "<p></p>", "<div><br></div>", "<div></div>", "<p>&nbsp;</p>"];

// Define the initial state using FormValues type
export const initialState: ExtendedFormValues = {
  title: "",
  employment_type: "",
  department_id: "",
  salary_range: "",
  salary_cycle: "",
  location_type: "",
  state: "",
  city: "",
  role_overview: "",
  experience_level: "",
  responsibilities: "",
  educations_requirement: "",
  certifications: undefined,
  skills_and_software_expertise: "",
  experience_required: "",
  ideal_candidate_traits: "",
  about_company: "",
  perks_benefits: undefined,
  tone_style: "",
  additional_info: undefined,
  compliance_statement: [],
  show_compliance: false,
  hiring_type: "",
};

// Define the skill item interface
export interface ISkillItem {
  id: number;
  title: string;
  description: string;
  short_description: string;
}

// Define a skill category interface
export interface ISkillCategory {
  type: string;
  items: ISkillItem[];
}

// Define the slice state type
export interface AllSkillsState {
  categories: ISkillCategory[];
  loading: boolean;
  error: string | null;
}

export const FILE_EXTENSION = [
  "pdf",
  "plain",
  "csv",
  "vnd.ms-excel.sheet.macroEnabled.12",
  "vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "vnd.openxmlformats-officedocument.wordprocessingml.document",
  "vnd.openxmlformats-officedocument.presentationml.presentation",
];

export const ACTIVE = "active";
export const TOKEN_EXPIRED = "Session Expired! Please log in again.";
export const DEFAULT_LIMIT = 15;
export const STANDARD_LIMIT = 18;
export const DEFAULT_OFFSET = 0;

export enum MessageType {
  success = "success",
  error = "error",
}

export const AGORA_CONNECTION_STATE = {
  DISCONNECTED: "Disconnected",
  CONNECTING: "Connecting",
  RECONNECTING: "Reconnecting",
  CONNECTED: "Connected",
  DISCONNECTING: "Disconnecting",
};

export const IMAGE_EXTENSIONS = ["png", "jpg", "jpeg", "gif", "webp"];

export const ASSESSMENT_INSTRUCTIONS = {
  instructions: [
    "Do not refresh or close the browser",
    "Check your internet connection",
    "Ensure a distraction-free environment",
    "Click 'Submit' only once when finished",
    "Read each question carefully",
    "Manage your time efficiently",
    "Avoid any form of plagiarism",
    "Reach out to support if needed",
  ],
};

export const PERMISSIONS_COOKIES_KEY = "permissions_data";

export const TYPING_PREVENTION_CHARACTERS = ["%", "_", "\\"];

export const AGORA_AUDIO_CONSTRAINTS = {
  AEC: true, // Echo cancellation
  ANS: true, // Automatic noise suppression
  AGC: true, // Automatic gain control
  encoderConfig: {
    sampleRate: 48000, // Higher sample rate for better quality
    stereo: false, // Use mono to avoid conflicts
    bitrate: 128, // Higher bitrate for interviewer
  },
};

export const AGORA_USER_TYPE = {
  Interviewer: "interviewer",
  Candidate: "candidate",
};

export const PDF_FILE_NAME = "pdf";
export const PDF_FILE_TYPE = "application/pdf";
export const PDF_FILE_SIZE_LIMIT = 5 * 1024 * 1024;
export const PDF_ADDITIONAL_SUBMISSION_LIMIT = 10854484;

export const LOG_TYPE_OPTIONS = [
  { value: "Login", label: "Login" },
  { value: "Add Employee", label: "Add Employee" },
  { value: "Change Access Role", label: "Change Access Role" },
  { value: "Update Permissions", label: "Update Permissions" },
  { value: "Job Posting", label: "Job Posting" },
  { value: "Subscription Update", label: "Subscription Update" },
  { value: "Schedule Interview", label: "Schedule Interview" },
  { value: "Interview Feedback", label: "Interview Feedback" },
  { value: "Hire/Reject Candidate", label: "Hire/Reject Candidate" },
  { value: "Candidate Final Summary", label: "Candidate Summary" },
];

export const INTERVIEW_FEEDBACK = {
  MODAL: {
    ADVANCE: {
      TITLE: "advance_candidate_title",
      MESSAGE: "advance_candidate_message",
      BUTTON: "advance_button",
    },
    REJECT: {
      TITLE: "reject_candidate_title",
      MESSAGE: "reject_candidate_message",
      BUTTON: "reject_button",
    },
  },
  BUTTON: {
    PROCESSING: "processing_text",
  },
};

export const SEARCH_REGEX = /^[^a-zA-Z0-9]+$/;
export const DEPARTMENT_NAME_REGEX = /^[a-zA-Z0-9\s\-_()\&\/]+$/;
export const SPACIAL_CHARACTERS = /[%_\\]/;
// export const SPACIAL_CHARACTERS = /[\/\\^$*+?()|[\]{}!#%&_=;:,'"><]/;  future use

export const STRATUM9_MAIN_WEB_URL = "https://stratum-nine.com";
