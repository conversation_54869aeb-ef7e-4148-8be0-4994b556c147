const ROUTES = {
  LOGIN: "/login",
  FORGOT_PASSWORD: "/forgot-password",
  VERIFY: "/verify",
  RESET_PASSWORD: "/reset-password",
  CANDIDATE_ASSESSMENT: "/candidate-assessment",
  CANDIDATE_JOIN: "/candidate-join",
  DASHBOARD: "/dashboard",
  HOME: "/",
  BUY_SUBSCRIPTION: "/buy-subscription",
  PROFILE: {
    MY_PROFILE: "/my-profile",
  },
  SUBSCRIPTIONS: {
    SUCCESS: "/subscriptions/success",
    CANCEL: "/subscriptions/cancel",
  },

  ARCHIVE: {
    ARCHIVE_JOBS_CANDIDATES: "/archive",
  },

  JOBS: {
    CAREER_BASED_SKILLS: "/career-based-skills",
    ROLE_BASED_SKILLS: "/role-based-skills",
    CULTURE_BASED_SKILLS: "/culture-based-skills",

    GENERATE_JOB: "/generate-job",
    EDIT_SKILLS: "/edit-skills",
    HIRING_TYPE: "/hiring-type",
    J<PERSON><PERSON>_EDITOR: "/job-editor",
    ACTIVE_JOBS: "/active-jobs",
    CA<PERSON><PERSON>ATE_PROFILE: "/candidate-profile",
    ARCHIVE: "/archive",
    PERFORMANCE_BASED_SKILLS: "/performance-based-skills",
  },
  SCREEN_RESUME: {
    MANUAL_CANDIDATE_UPLOAD: "/manual-upload-resume",
    CANDIDATE_QUALIFICATION: "/candidate-qualification",
    CANDIDATE_LIST: "/candidates-list",
    HIRED_CANDIDATES: "/hired-candidates",
    // CANDIDATES: "/candidates",
  },
  INTERVIEW: {
    ADD_CANDIDATE_INFO: "/additional-submission",
    SCHEDULE_INTERVIEW: "/schedule-interview",
    PRE_INTERVIEW_QUESTIONS_OVERVIEW: "/pre-interview-questions-overview",
    INTERVIEW_QUESTION: "/interview-question",
    CALENDAR: "/calendar",
    INTERVIEW_SUMMARY: "/interview-summary",
    INTERVIEW: "/interview",
    INTERVIEW_FEEDBACK: "/interview-feedback",
  },

  ROLE_EMPLOYEES: {
    ROLES_PERMISSIONS: "/roles-permissions",
    EMPLOYEE_MANAGEMENT: "/employee-management",
    EMPLOYEE_MANAGEMENT_DETAIL: "/employee-management-detail",
    ADD_EMPLOYEE: "/add-employees",
    ADD_DEPARTMENT: "/add-department",
  },

  FINAL_ASSESSMENT: {
    FINAL_ASSESSMENT: "/final-assessment",
  },
  ACTIVITY_LOGS: "/activity-logs",
};

export const BEFORE_LOGIN_ROUTES = [
  ROUTES.HOME,
  ROUTES.LOGIN,
  ROUTES.FORGOT_PASSWORD,
  ROUTES.VERIFY,
  ROUTES.RESET_PASSWORD,
  ROUTES.CANDIDATE_ASSESSMENT,
  ROUTES.CANDIDATE_JOIN,
];

// Routes that don't require permission checks for authenticated users
export const UNRESTRICTED_ROUTES = [ROUTES.SUBSCRIPTIONS.SUCCESS, ROUTES.SUBSCRIPTIONS.CANCEL, ROUTES.INTERVIEW.INTERVIEW_FEEDBACK];

export default ROUTES;
